import React, { useState } from 'react';
import { addChangelogEntries } from '../utils/addChangelogEntries';

const AddChangelogButton = () => {
  const [isAdding, setIsAdding] = useState(false);
  const [status, setStatus] = useState('');

  const addCriticalFixesEntry = async () => {
    setIsAdding(true);
    setStatus('Adding changelog entries...');

    try {
      const result = await addChangelogEntries();

      if (result.success) {
        setStatus('✅ Successfully added all changelog entries! Close and reopen the changelog to see them.');
      } else {
        setStatus('❌ Error: ' + result.error);
      }

    } catch (error) {
      console.error('Error adding changelog entries:', error);
      setStatus('❌ Error: ' + error.message);
    } finally {
      setIsAdding(false);
    }
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: '20px', 
      right: '20px', 
      zIndex: 9999,
      background: 'white',
      padding: '15px',
      borderRadius: '8px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      border: '2px solid #e63946'
    }}>
      <h4 style={{ margin: '0 0 10px 0', color: '#e63946' }}>Admin: Add Changelog</h4>

      <button
        onClick={addCriticalFixesEntry}
        disabled={isAdding}
        style={{
          background: isAdding ? '#ccc' : '#e63946',
          color: 'white',
          border: 'none',
          padding: '8px 16px',
          borderRadius: '4px',
          cursor: isAdding ? 'not-allowed' : 'pointer',
          marginBottom: '10px',
          display: 'block',
          width: '100%'
        }}
      >
        {isAdding ? 'Adding...' : 'Add All Changelog Entries'}
      </button>
      {status && (
        <p style={{ 
          margin: '0', 
          fontSize: '12px', 
          color: status.includes('✅') ? 'green' : status.includes('❌') ? 'red' : 'blue',
          wordWrap: 'break-word',
          maxWidth: '250px'
        }}>
          {status}
        </p>
      )}
    </div>
  );
};

export default AddChangelogButton;
