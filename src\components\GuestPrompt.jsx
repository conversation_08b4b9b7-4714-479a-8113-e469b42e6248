import React from 'react';
import { useAuth } from '../AuthContext';
import './GuestPrompt.css';

/**
 * Guest Prompt Component
 * Shows a friendly message encouraging guests to sign up for interactive features
 */
const GuestPrompt = ({ 
  title = "Join the Community", 
  message = "Sign up to unlock this feature and connect with the NAROOP community!",
  icon = "✨",
  showButtons = true,
  className = ""
}) => {
  const { exitGuestMode } = useAuth();

  const handleSignUp = () => {
    exitGuestMode();
  };

  const handleSignIn = () => {
    exitGuestMode();
  };

  return (
    <div className={`guest-prompt ${className}`}>
      <div className="guest-prompt-content">
        <div className="guest-prompt-icon">{icon}</div>
        <h3 className="guest-prompt-title">{title}</h3>
        <p className="guest-prompt-message">{message}</p>
        
        {showButtons && (
          <div className="guest-prompt-actions">
            <button 
              className="guest-prompt-signup"
              onClick={handleSignUp}
            >
              Sign Up Free
            </button>
            <button 
              className="guest-prompt-signin"
              onClick={handleSignIn}
            >
              Sign In
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default GuestPrompt;
