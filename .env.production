# Production Environment Variables for Naroop
# These are the default production values
# Override these in Netlify dashboard for actual deployment

# Application Settings
VITE_APP_ENV=production
VITE_APP_VERSION=1.0.0
VITE_APP_NAME=Naroop

# Build Settings
# NODE_ENV is automatically set by Vite for production builds

# Feature Flags for Production
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_KIDS_SECTION=true
VITE_ENABLE_COMMUNITY_FEATURES=true
VITE_ENABLE_MESSAGING=true

# Performance Settings
VITE_ENABLE_SOURCE_MAPS=true
VITE_ENABLE_BUNDLE_ANALYZER=false
