/* Friend Request Modal Styles */
.friend-request-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 1rem;
  opacity: 1;
  transition: opacity 0.2s ease-out;
}

.friend-request-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-top: 4px solid var(--color-heritage-gold);
}

.friend-request-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #fff9e6 100%);
}

.friend-request-modal-header h3 {
  margin: 0;
  color: var(--color-dark);
  font-size: 1.3rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.friend-request-modal-header h3::before {
  content: '👥';
  font-size: 1.2em;
}

.friend-request-modal .close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #718096;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  transition: all 0.2s ease;
}

.friend-request-modal .close-btn:hover:not(:disabled) {
  background: #e2e8f0;
  color: #2d3748;
  transform: scale(1.1);
}

.friend-request-modal .close-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.friend-request-modal-body {
  padding: 2rem;
  overflow-y: auto;
  flex: 1;
  min-height: 200px;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-state .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-accent-emerald);
  border-top: 3px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.loading-state p {
  color: var(--color-gray-medium);
  margin: 0;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-state p {
  color: var(--color-gray-medium);
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.sender-info-fallback {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

/* Request Content */
.request-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sender-info {
  display: flex;
  justify-content: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #fff9e6 100%);
  border-radius: 12px;
  border: 1px solid rgba(184, 134, 11, 0.2);
}

.sender-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.request-message {
  background: #fff9e6;
  border: 1px solid rgba(184, 134, 11, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid var(--color-heritage-gold);
}

.request-message h4 {
  margin: 0 0 0.5rem 0;
  color: var(--color-dark);
  font-size: 1rem;
  font-weight: 600;
}

.request-message p {
  margin: 0;
  color: var(--color-gray-dark);
  font-style: italic;
  line-height: 1.5;
  font-size: 1rem;
}

.request-info {
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.request-time {
  margin: 0;
  color: var(--color-gray-medium);
  font-size: 0.9rem;
}

/* Modal Actions */
.friend-request-modal-actions {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.accept-btn,
.decline-btn,
.close-only-btn {
  flex: 1;
  max-width: 150px;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.accept-btn {
  background: linear-gradient(135deg, var(--color-accent-emerald), #10b981);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.accept-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #10b981, var(--color-accent-emerald));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.decline-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.decline-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.close-only-btn {
  background: var(--color-gray-medium);
  color: white;
}

.close-only-btn:hover {
  background: var(--color-gray-dark);
  transform: translateY(-2px);
}

.accept-btn:disabled,
.decline-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 600px) {
  .friend-request-modal-overlay {
    padding: 0.5rem;
  }
  
  .friend-request-modal {
    max-width: 100%;
    margin: 0;
  }
  
  .friend-request-modal-header,
  .friend-request-modal-body,
  .friend-request-modal-actions {
    padding: 1rem;
  }
  
  .friend-request-modal-actions {
    flex-direction: column;
  }
  
  .accept-btn,
  .decline-btn,
  .close-only-btn {
    max-width: none;
  }
}

/* Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .friend-request-modal {
    background: #2d3748;
    color: #f7fafc;
  }
  
  .friend-request-modal-header {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    border-bottom-color: #4a5568;
  }
  
  .friend-request-modal-header h3 {
    color: #f7fafc;
  }
  
  .friend-request-modal-actions {
    background: #2d3748;
    border-top-color: #4a5568;
  }
  
  .sender-info,
  .request-info {
    background: #4a5568;
  }
  
  .request-message {
    background: #4a5568;
    border-color: var(--color-heritage-gold);
  }
}
