# Naroop Deployment Workflow

## Overview

Naroop is configured for automatic deployment to Netlify with optimized build settings, security headers, and Firebase integration.

## Automatic Deployment Triggers

### 1. Main Branch Deployments (Production)
- **Trigger**: Push to `main` branch
- **Action**: Automatic production deployment
- **URL**: Your primary Netlify domain
- **Build Command**: `npm run build`
- **Publish Directory**: `dist`

### 2. Deploy Previews
- **Trigger**: Pull requests to `main` branch
- **Action**: Creates preview deployment
- **URL**: Unique preview URL (e.g., `deploy-preview-123--your-site.netlify.app`)
- **Purpose**: Test changes before merging

### 3. Branch Deployments
- **Trigger**: Push to any other branch (if enabled)
- **Action**: Branch-specific deployment
- **URL**: Branch-specific URL (e.g., `feature-branch--your-site.netlify.app`)

## Deployment Process

### Step-by-Step Workflow

1. **Code Changes**
   ```bash
   # Make your changes
   git add .
   git commit -m "Your commit message"
   ```

2. **Local Testing** (Recommended)
   ```bash
   # Test build locally
   npm run build
   npm run preview
   ```

3. **Push to Repository**
   ```bash
   # Push to main for production deployment
   git push origin main
   
   # Or push to feature branch for branch deployment
   git push origin feature-branch
   ```

4. **Netlify Build Process**
   - Netlify detects the push
   - Runs `npm install` to install dependencies
   - Executes `npm run build` command
   - Publishes `dist` folder contents
   - Applies security headers and redirects

## Build Configuration

### Netlify Settings (netlify.toml)
- **Build Command**: `npm run build`
- **Publish Directory**: `dist`
- **Node Version**: 18
- **Environment**: Production optimized

### Vite Build Optimization
- Bundle splitting (vendor, firebase chunks)
- Terser minification
- Source maps for debugging
- Modern ES2015 target
- Optimized asset caching

## Environment Variables Setup

### Required for Production
Set these in Netlify Dashboard → Site Settings → Environment Variables:

```
VITE_FIREBASE_API_KEY=your_actual_api_key
VITE_FIREBASE_AUTH_DOMAIN=naroop-451d1.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=naroop-451d1
VITE_FIREBASE_STORAGE_BUCKET=naroop-451d1.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=280835864133
VITE_FIREBASE_APP_ID=1:280835864133:web:5fcf2953fca215a721152c
VITE_FIREBASE_MEASUREMENT_ID=G-E82HQ5J8T4
```

## Security & Performance Features

### Security Headers
- Content Security Policy
- XSS Protection
- Clickjacking Prevention
- MIME Type Sniffing Protection

### Performance Optimization
- Static asset caching (1 year)
- Bundle splitting for better caching
- Lighthouse monitoring
- Gzip compression

## Monitoring & Debugging

### Build Logs
- Access via Netlify Dashboard → Deploys
- Monitor for build errors or warnings
- Check bundle size and performance metrics

### Error Tracking
- Source maps enabled for debugging
- Console errors visible in browser dev tools
- Netlify function logs (if using functions)

## Best Practices

### Before Deploying
1. ✅ Test locally with `npm run build && npm run preview`
2. ✅ Check for console errors
3. ✅ Verify Firebase connection works
4. ✅ Test responsive design
5. ✅ Run linting with `npm run lint`

### Git Workflow
1. Create feature branches for new features
2. Use pull requests for code review
3. Test deploy previews before merging
4. Keep main branch stable and deployable

### Environment Management
1. Never commit sensitive keys to Git
2. Use environment variables for all config
3. Test with production-like data when possible
4. Monitor Firebase usage and quotas

## Troubleshooting

### Common Issues

1. **Build Fails**
   - Check environment variables are set
   - Verify all dependencies are in package.json
   - Check build logs for specific errors

2. **Firebase Connection Issues**
   - Verify environment variables match Firebase console
   - Check Firebase project settings
   - Ensure Firebase rules allow your domain

3. **Routing Issues**
   - Verify `_redirects` file is in public folder
   - Check netlify.toml redirect configuration
   - Test SPA routing in preview

4. **Performance Issues**
   - Check Lighthouse scores in deploy logs
   - Monitor bundle sizes
   - Optimize images and assets

### Getting Help
- Check Netlify deploy logs
- Review Firebase console for errors
- Use browser dev tools for debugging
- Check this documentation for configuration details
