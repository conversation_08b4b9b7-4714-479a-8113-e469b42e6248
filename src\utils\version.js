/**
 * Version Management Utility
 * Provides version information for the Naroop application
 */

import packageJson from '../../package.json';

/**
 * Get the current application version from package.json
 * @returns {string} The current version number
 */
export const getAppVersion = () => {
  return packageJson.version;
};

/**
 * Get the application name from package.json
 * @returns {string} The application name
 */
export const getAppName = () => {
  return packageJson.name;
};

/**
 * Get formatted version display string
 * @returns {string} Formatted version string (e.g., "v1.0.0")
 */
export const getFormattedVersion = () => {
  return `v${getAppVersion()}`;
};

/**
 * Get full application info
 * @returns {object} Object containing name and version
 */
export const getAppInfo = () => {
  return {
    name: getAppName(),
    version: getAppVersion(),
    formattedVersion: getFormattedVersion()
  };
};

/**
 * Check if this is a development build
 * @returns {boolean} True if in development mode
 */
export const isDevelopment = () => {
  return import.meta.env.DEV;
};

/**
 * Get build environment info
 * @returns {string} Environment name (development/production)
 */
export const getBuildEnvironment = () => {
  return import.meta.env.DEV ? 'development' : 'production';
};
