/**
 * Add changelog entry for version 1.1.2
 * Security improvements and admin system cleanup
 */

import { db } from '../firebase';
import { collection, addDoc, getDocs, query, where } from 'firebase/firestore';

export const addV112ChangelogIfNeeded = async () => {
  try {
    console.log('🔒 Checking for v1.1.2 changelog entry...');
    
    const changelogRef = collection(db, 'changelog');
    
    // Check if v1.1.2 already exists
    const v112Query = query(changelogRef, where('version', '==', '1.1.2'));
    const v112Snapshot = await getDocs(v112Query);
    
    if (v112Snapshot.empty) {
      // Add v1.1.2 entry
      const v112Entry = {
        version: '1.1.2',
        title: 'Security Improvements & Admin System Cleanup 🔒',
        description: 'Enhanced platform security with improved admin role management and removed development tools from production for better safety.',
        category: 'security',
        releaseDate: new Date().toISOString(),
        changes: [
          'Improved admin role assignment - platform owner role now automatically assigned on login',
          'Removed development testing tools from production for enhanced security',
          'Enhanced automatic admin system initialization for smoother user experience',
          'Streamlined admin dashboard access for authorized users',
          'Better security checks to prevent unauthorized access to admin features',
          'Cleaned up development-only components that were not needed in production',
          'Improved platform owner authentication and role verification',
          'Enhanced overall platform security and user safety'
        ],
        isBreaking: false,
        timestamp: Date.now()
      };
      
      await addDoc(changelogRef, v112Entry);
      console.log('✅ Added v1.1.2 changelog entry');
      return { success: true, message: 'v1.1.2 changelog entry added successfully' };
    } else {
      console.log('ℹ️ v1.1.2 entry already exists');
      return { success: true, message: 'v1.1.2 changelog entry already exists' };
    }
    
  } catch (error) {
    console.error('❌ Error adding v1.1.2 changelog:', error);
    return { success: false, error: error.message };
  }
};
