<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />

    <!-- Enhanced viewport configuration for mobile -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />

    <!-- Mobile-specific meta tags -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="NAROOP" />
    <meta name="theme-color" content="#e63946" />
    <meta name="msapplication-TileColor" content="#e63946" />

    <!-- SEO and social media meta tags -->
    <title>NAROOP - Narrative of Our People | Uplifting Black Voices</title>
    <meta name="description" content="A positive space for Black voices to be heard through stories, experiences, and encouragement. Join our community celebrating greatness, unity, and growth." />
    <meta name="keywords" content="Black community, stories, empowerment, unity, positive space, African American, community support" />

    <!-- Open Graph meta tags for social sharing -->
    <meta property="og:title" content="NAROOP - Narrative of Our People" />
    <meta property="og:description" content="A positive space for Black voices to be heard through stories, experiences, and encouragement." />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="NAROOP" />

    <!-- Twitter Card meta tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="NAROOP - Narrative of Our People" />
    <meta name="twitter:description" content="A positive space for Black voices to be heard through stories, experiences, and encouragement." />

    <!-- Preload critical fonts for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Preload critical resources -->
    <link rel="modulepreload" href="/src/main.jsx" />
    <link rel="modulepreload" href="/src/App.jsx" />

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="https://firebase.googleapis.com" />
    <link rel="dns-prefetch" href="https://firestore.googleapis.com" />

    <!-- Critical CSS inline (will be replaced by Vite in production) -->
    <style>
      /* Critical above-the-fold styles */
      body {
        margin: 0;
        padding: 0;
        font-family: system-ui, -apple-system, sans-serif;
        background: linear-gradient(135deg, #f7fafc 0%, #10b981 100%);
        min-height: 100vh;
      }

      #root {
        min-height: 100vh;
      }

      /* Loading state styles */
      .loading-spinner {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        min-height: 200px;
      }

      .loading-spinner::before {
        content: '';
        width: 40px;
        height: 40px;
        border: 3px solid #10b981;
        border-top: 3px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Loading fallback for better perceived performance -->
      <div class="loading-spinner" style="min-height: 100vh;">
        <div style="text-align: center; color: #374151;">
          <div style="margin-bottom: 16px;">Loading NAROOP...</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.jsx"></script>

    <!-- Preload critical chunks (will be updated by Vite build) -->
    <script>
      // Performance monitoring
      if ('performance' in window) {
        window.addEventListener('load', () => {
          // Log Core Web Vitals for monitoring
          setTimeout(() => {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
              console.log('Performance metrics:', {
                FCP: performance.getEntriesByName('first-contentful-paint')[0]?.startTime,
                LCP: performance.getEntriesByType('largest-contentful-paint')[0]?.startTime,
                FID: performance.getEntriesByType('first-input')[0]?.processingStart,
                CLS: performance.getEntriesByType('layout-shift').reduce((sum, entry) => sum + entry.value, 0)
              });
            }
          }, 1000);
        });
      }
    </script>
  </body>
</html>
