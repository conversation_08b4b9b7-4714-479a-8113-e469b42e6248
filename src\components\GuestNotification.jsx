import React, { useState } from 'react';
import { useAuth } from '../AuthContext';
import './GuestNotification.css';

/**
 * Guest Notification Component
 * Shows a persistent notification bar for guest users explaining limitations
 * and encouraging them to sign up for full functionality
 */
const GuestNotification = () => {
  const { exitGuestMode } = useAuth();
  const [isMinimized, setIsMinimized] = useState(false);

  const handleSignUp = () => {
    exitGuestMode();
    // This will return to landing page where they can sign up
  };

  const handleSignIn = () => {
    exitGuestMode();
    // This will return to landing page where they can sign in
  };

  if (isMinimized) {
    return (
      <div className="guest-notification minimized">
        <button 
          className="expand-btn"
          onClick={() => setIsMinimized(false)}
          title="Show guest information"
        >
          👋 Guest Mode
        </button>
      </div>
    );
  }

  return (
    <div className="guest-notification">
      <div className="guest-notification-content">
        <div className="guest-info">
          <span className="guest-icon">👋</span>
          <div className="guest-text">
            <strong>Browsing as Guest</strong>
            <p>You can read stories and explore the community. To share stories, react, or connect with others, please sign up!</p>
          </div>
        </div>
        
        <div className="guest-actions">
          <button 
            className="guest-signup-btn"
            onClick={handleSignUp}
          >
            Sign Up Free
          </button>
          <button 
            className="guest-signin-btn"
            onClick={handleSignIn}
          >
            Sign In
          </button>
          <button 
            className="minimize-btn"
            onClick={() => setIsMinimized(true)}
            title="Minimize notification"
          >
            ✕
          </button>
        </div>
      </div>
    </div>
  );
};

export default GuestNotification;
