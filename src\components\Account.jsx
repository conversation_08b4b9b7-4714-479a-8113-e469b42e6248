import React, { useContext, useState, useEffect } from 'react';
import { AuthContext } from '../AuthContext';
import { auth, db } from '../firebase';
import { doc, updateDoc, getDoc, setDoc } from 'firebase/firestore';
import { useNavigate } from 'react-router-dom';

const Account = () => {
  const { currentUser } = useContext(AuthContext);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editing, setEditing] = useState(false);
  const [editForm, setEditForm] = useState({});
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to home if not authenticated
    if (!currentUser) {
      navigate('/');
      return;
    }

    const fetchProfile = async () => {
      try {
        const ref = doc(db, 'users', currentUser.uid);
        const snap = await getDoc(ref);
        if (snap.exists()) {
          const profileData = { ...snap.data(), id: currentUser.uid };
          setUserProfile(profileData);
          setEditForm(profileData);
        } else {
          // Create a default profile if missing
          const defaultProfile = {
            email: currentUser.email,
            name: currentUser.displayName || '',
            avatar: '👤',
            joinDate: new Date().toISOString(),
            totalStories: 0,
            totalReactions: 0,
            badges: [],
            bio: '',
            roleModel: '',
            tradition: '',
            dream: '',
            skills: [],
            interests: [],
            goals: '',
            mentorAvailable: false
          };
          await setDoc(ref, defaultProfile);
          const profileData = { ...defaultProfile, id: currentUser.uid };
          setUserProfile(profileData);
          setEditForm(profileData);
        }
      } catch (err) {
        setError('Failed to load profile: ' + err.message);
        console.error('Profile fetch error:', err);
      }
      setLoading(false);
    };
    fetchProfile();
  }, [currentUser, navigate]);

  const handleProfileUpdate = async (updates) => {
    if (!currentUser) return;
    try {
      const ref = doc(db, 'users', currentUser.uid);
      await updateDoc(ref, updates);
      setUserProfile(prev => ({ ...prev, ...updates }));
      setEditForm(prev => ({ ...prev, ...updates }));
    } catch (err) {
      setError('Failed to update profile: ' + err.message);
    }
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    await handleProfileUpdate(editForm);
    setEditing(false);
  };

  const handleLogout = async () => {
    await auth.signOut();
    navigate('/');
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!currentUser) {
    return null; // Will redirect via useEffect
  }

  if (loading) {
    return (
      <div className="account-page">
        <div className="account-loading">
          <div className="loading-spinner"></div>
          <p>Loading your profile...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="account-page">
        <div className="account-error">
          <h2>Error</h2>
          <p>{error}</p>
          <button onClick={() => window.location.reload()}>Try Again</button>
        </div>
      </div>
    );
  }

  return (
    <div className="account-page">
      <header className="account-header">
        <div className="account-header-content">
          <button
            onClick={() => navigate('/')}
            className="back-to-home-btn"
            aria-label="Back to Home"
          >
            ← Back to NAROOP
          </button>
          <h1>My Account</h1>
          <button
            onClick={handleLogout}
            className="logout-btn"
            aria-label="Log out"
          >
            Log Out
          </button>
        </div>
      </header>

      <main className="account-content">
        {/* Basic Profile Information */}
        <section className="profile-section">
          <div className="section-header">
            <h2>Profile Information</h2>
            <button
              onClick={() => setEditing(!editing)}
              className="edit-profile-btn"
            >
              {editing ? 'Cancel' : 'Edit Profile'}
            </button>
          </div>

          {editing ? (
            <form onSubmit={handleEditSubmit} className="profile-edit-form">
              <div className="form-grid">
                <div className="form-group">
                  <label htmlFor="name">Display Name</label>
                  <input
                    type="text"
                    id="name"
                    value={editForm.name || ''}
                    onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                    maxLength={30}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="avatar">Avatar Emoji</label>
                  <input
                    type="text"
                    id="avatar"
                    value={editForm.avatar || ''}
                    onChange={(e) => setEditForm(prev => ({ ...prev, avatar: e.target.value }))}
                    maxLength={2}
                    placeholder="👤"
                  />
                </div>

                <div className="form-group full-width">
                  <label htmlFor="bio">Bio</label>
                  <textarea
                    id="bio"
                    value={editForm.bio || ''}
                    onChange={(e) => setEditForm(prev => ({ ...prev, bio: e.target.value }))}
                    maxLength={200}
                    placeholder="Tell us about yourself..."
                    rows={3}
                  />
                </div>
              </div>

              <div className="form-actions">
                <button type="submit" className="save-btn">Save Changes</button>
                <button type="button" onClick={() => setEditing(false)} className="cancel-btn">
                  Cancel
                </button>
              </div>
            </form>
          ) : (
            <div className="profile-display">
              <div className="profile-avatar-section">
                <div className="profile-avatar">{userProfile.avatar || '👤'}</div>
                <div className="profile-basic-info">
                  <h3>{userProfile.name || 'Anonymous User'}</h3>
                  <p className="profile-email">{userProfile.email}</p>
                  <p className="profile-join-date">Member since {formatDate(userProfile.joinDate)}</p>
                </div>
              </div>

              {userProfile.bio && (
                <div className="profile-bio">
                  <h4>About Me</h4>
                  <p>{userProfile.bio}</p>
                </div>
              )}
            </div>
          )}
        </section>

        {/* Community Profile Section */}
        <section className="profile-section">
          <div className="section-header">
            <h2>Community Profile</h2>
          </div>

          <div className="community-profile-grid">
            {userProfile.roleModel && (
              <div className="community-field">
                <h4>Role Model</h4>
                <p>{userProfile.roleModel}</p>
              </div>
            )}

            {userProfile.tradition && (
              <div className="community-field">
                <h4>Cherished Tradition/Value</h4>
                <p>{userProfile.tradition}</p>
              </div>
            )}

            {userProfile.dream && (
              <div className="community-field">
                <h4>Dream/Goal</h4>
                <p>{userProfile.dream}</p>
              </div>
            )}

            {userProfile.goals && (
              <div className="community-field">
                <h4>Personal Goals</h4>
                <p>{userProfile.goals}</p>
              </div>
            )}

            {userProfile.skills && userProfile.skills.length > 0 && (
              <div className="community-field">
                <h4>Skills</h4>
                <div className="tags-list">
                  {userProfile.skills.map((skill, index) => (
                    <span key={index} className="tag">{skill}</span>
                  ))}
                </div>
              </div>
            )}

            {userProfile.interests && userProfile.interests.length > 0 && (
              <div className="community-field">
                <h4>Interests</h4>
                <div className="tags-list">
                  {userProfile.interests.map((interest, index) => (
                    <span key={index} className="tag">{interest}</span>
                  ))}
                </div>
              </div>
            )}

            <div className="community-field">
              <h4>Mentorship</h4>
              <p className={`mentor-status ${userProfile.mentorAvailable ? 'available' : 'unavailable'}`}>
                {userProfile.mentorAvailable ? '✅ Available as Mentor' : '❌ Not Available as Mentor'}
              </p>
            </div>
          </div>
        </section>

        {/* Account Overview */}
        <section className="profile-section">
          <div className="section-header">
            <h2>📊 Account Overview</h2>
          </div>

          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">🎯</div>
              <div className="stat-number">{userProfile.totalStories || 0}</div>
              <div className="stat-label">Stories Shared</div>
            </div>

            <div className="stat-card">
              <div className="stat-icon">📦</div>
              <div className="stat-number">{userProfile.totalReactions || 0}</div>
              <div className="stat-label">Reactions Received</div>
            </div>

            <div className="stat-card">
              <div className="stat-icon">🗑️</div>
              <div className="stat-number">{userProfile.badges ? userProfile.badges.length : 0}</div>
              <div className="stat-label">Badges Earned</div>
            </div>

            <div className="stat-card">
              <div className="stat-icon">📅</div>
              <div className="stat-number">
                {userProfile.joinDate ? Math.floor((new Date() - new Date(userProfile.joinDate)) / (1000 * 60 * 60 * 24)) : 0}
              </div>
              <div className="stat-label">Days as Member</div>
            </div>
          </div>

          {userProfile.badges && userProfile.badges.length > 0 && (
            <div className="badges-section">
              <h4>Earned Badges</h4>
              <div className="badges-list">
                {userProfile.badges.map((badge, index) => (
                  <div key={index} className="badge">
                    <span className="badge-icon">{badge.icon || '🏆'}</span>
                    <span className="badge-name">{badge.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </section>

        {/* Account Settings */}
        <section className="profile-section">
          <div className="section-header">
            <h2>Account Settings</h2>
          </div>

          <div className="settings-grid">
            <div className="setting-item">
              <h4>Email Address</h4>
              <p>{userProfile.email}</p>
              <small>Contact support to change your email address</small>
            </div>

            <div className="setting-item">
              <h4>Account Created</h4>
              <p>{formatDate(userProfile.joinDate)}</p>
            </div>

            <div className="setting-item">
              <h4>Profile Visibility</h4>
              <p>Public to NAROOP community</p>
              <small>Your profile is visible to other community members</small>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default Account;
