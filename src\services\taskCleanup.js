import { db } from '../firebase';
import { 
  doc, 
  updateDoc, 
  getDoc, 
  collection, 
  query, 
  where, 
  getDocs, 
  writeBatch,
  serverTimestamp
} from 'firebase/firestore';

/**
 * Smart Task Cleanup Service
 * Handles auto-archiving, bulk operations, and task management
 */

/**
 * Auto-archive completed financial goals after 30 days
 */
export async function autoArchiveCompletedGoals(userId) {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      throw new Error('User not found');
    }

    const userData = userDoc.data();
    const goals = userData.financialGoals || [];
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    let hasChanges = false;
    const updatedGoals = goals.map(goal => {
      // Check if goal is 100% complete
      const totalMilestones = goal.milestones.length;
      const completedMilestones = goal.milestones.filter(m => m.completed).length;
      const isFullyCompleted = totalMilestones > 0 && completedMilestones === totalMilestones;

      // Check if it's been 30 days since completion
      if (isFullyCompleted && !goal.archived) {
        // Find the latest milestone completion date
        const completedMilestonesDates = goal.milestones
          .filter(m => m.completed && m.completedAt)
          .map(m => new Date(m.completedAt))
          .sort((a, b) => b - a);

        const lastCompletionDate = completedMilestonesDates[0];
        
        if (lastCompletionDate && lastCompletionDate < thirtyDaysAgo) {
          hasChanges = true;
          return {
            ...goal,
            archived: true,
            archivedAt: new Date().toISOString(),
            archivedReason: 'auto_completed_30_days'
          };
        }
      }

      return goal;
    });

    if (hasChanges) {
      await updateDoc(userRef, {
        financialGoals: updatedGoals
      });

      return {
        success: true,
        archivedCount: updatedGoals.filter(g => g.archived && !goals.find(og => og.id === g.id)?.archived).length,
        message: 'Completed goals auto-archived successfully'
      };
    }

    return {
      success: true,
      archivedCount: 0,
      message: 'No goals needed archiving'
    };
  } catch (error) {
    console.error('Error auto-archiving goals:', error);
    throw error;
  }
}

/**
 * Mark support request as resolved and archive it
 */
export async function markSupportRequestResolved(requestId, userId, resolution = '') {
  try {
    const requestRef = doc(db, 'supportRequests', requestId);
    const requestDoc = await getDoc(requestRef);
    
    if (!requestDoc.exists()) {
      throw new Error('Support request not found');
    }

    const requestData = requestDoc.data();
    
    // Verify ownership
    if (requestData.authorId !== userId) {
      throw new Error('You can only resolve your own support requests');
    }

    await updateDoc(requestRef, {
      status: 'resolved',
      resolvedAt: new Date().toISOString(),
      resolvedBy: userId,
      resolution: resolution,
      archived: true
    });

    return {
      success: true,
      message: 'Support request marked as resolved and archived'
    };
  } catch (error) {
    console.error('Error resolving support request:', error);
    throw error;
  }
}

/**
 * Bulk delete multiple items
 */
export async function bulkDeleteItems(items, contentType, userId) {
  try {
    const batch = writeBatch(db);
    const results = [];

    for (const item of items) {
      switch (contentType) {
        case 'supportRequests':
          const requestRef = doc(db, 'supportRequests', item.id);
          const requestDoc = await getDoc(requestRef);
          
          if (requestDoc.exists()) {
            const requestData = requestDoc.data();
            if (requestData.authorId === userId) {
              batch.delete(requestRef);
              results.push({ id: item.id, success: true });
            } else {
              results.push({ id: item.id, success: false, error: 'Not authorized' });
            }
          } else {
            results.push({ id: item.id, success: false, error: 'Not found' });
          }
          break;

        case 'discussions':
          const discussionRef = doc(db, 'discussions', item.id);
          const discussionDoc = await getDoc(discussionRef);
          
          if (discussionDoc.exists()) {
            const discussionData = discussionDoc.data();
            if (discussionData.authorId === userId) {
              batch.delete(discussionRef);
              results.push({ id: item.id, success: true });
            } else {
              results.push({ id: item.id, success: false, error: 'Not authorized' });
            }
          } else {
            results.push({ id: item.id, success: false, error: 'Not found' });
          }
          break;

        case 'campaigns':
          const campaignRef = doc(db, 'activismCampaigns', item.id);
          const campaignDoc = await getDoc(campaignRef);
          
          if (campaignDoc.exists()) {
            const campaignData = campaignDoc.data();
            if (campaignData.creatorId === userId) {
              batch.delete(campaignRef);
              results.push({ id: item.id, success: true });
            } else {
              results.push({ id: item.id, success: false, error: 'Not authorized' });
            }
          } else {
            results.push({ id: item.id, success: false, error: 'Not found' });
          }
          break;

        default:
          results.push({ id: item.id, success: false, error: 'Unknown content type' });
      }
    }

    await batch.commit();

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return {
      success: true,
      results,
      successCount,
      failureCount,
      message: `Bulk delete completed: ${successCount} deleted, ${failureCount} failed`
    };
  } catch (error) {
    console.error('Error in bulk delete:', error);
    throw error;
  }
}

/**
 * Bulk archive multiple items
 */
export async function bulkArchiveItems(items, contentType, userId) {
  try {
    const batch = writeBatch(db);
    const results = [];

    for (const item of items) {
      switch (contentType) {
        case 'supportRequests':
          const requestRef = doc(db, 'supportRequests', item.id);
          const requestDoc = await getDoc(requestRef);
          
          if (requestDoc.exists()) {
            const requestData = requestDoc.data();
            if (requestData.authorId === userId) {
              batch.update(requestRef, {
                archived: true,
                archivedAt: new Date().toISOString(),
                archivedBy: userId
              });
              results.push({ id: item.id, success: true });
            } else {
              results.push({ id: item.id, success: false, error: 'Not authorized' });
            }
          } else {
            results.push({ id: item.id, success: false, error: 'Not found' });
          }
          break;

        case 'discussions':
          const discussionRef = doc(db, 'discussions', item.id);
          const discussionDoc = await getDoc(discussionRef);
          
          if (discussionDoc.exists()) {
            const discussionData = discussionDoc.data();
            if (discussionData.authorId === userId) {
              batch.update(discussionRef, {
                archived: true,
                archivedAt: new Date().toISOString(),
                archivedBy: userId
              });
              results.push({ id: item.id, success: true });
            } else {
              results.push({ id: item.id, success: false, error: 'Not authorized' });
            }
          } else {
            results.push({ id: item.id, success: false, error: 'Not found' });
          }
          break;

        case 'financialGoals':
          // Handle financial goals differently since they're stored in user document
          const userRef = doc(db, 'users', userId);
          const userDoc = await getDoc(userRef);
          
          if (userDoc.exists()) {
            const userData = userDoc.data();
            const goals = userData.financialGoals || [];
            const updatedGoals = goals.map(goal => {
              if (items.some(item => item.id === goal.id)) {
                return {
                  ...goal,
                  archived: true,
                  archivedAt: new Date().toISOString(),
                  archivedBy: userId
                };
              }
              return goal;
            });

            batch.update(userRef, { financialGoals: updatedGoals });
            items.forEach(item => {
              results.push({ id: item.id, success: true });
            });
          } else {
            items.forEach(item => {
              results.push({ id: item.id, success: false, error: 'User not found' });
            });
          }
          break;

        default:
          results.push({ id: item.id, success: false, error: 'Unknown content type' });
      }
    }

    await batch.commit();

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return {
      success: true,
      results,
      successCount,
      failureCount,
      message: `Bulk archive completed: ${successCount} archived, ${failureCount} failed`
    };
  } catch (error) {
    console.error('Error in bulk archive:', error);
    throw error;
  }
}

/**
 * Clean up old archived items (delete items archived for more than 1 year)
 */
export async function cleanupOldArchivedItems(userId) {
  try {
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

    let cleanupCount = 0;

    // Clean up support requests
    const requestsQuery = query(
      collection(db, 'supportRequests'),
      where('authorId', '==', userId),
      where('archived', '==', true)
    );
    
    const requestsSnapshot = await getDocs(requestsQuery);
    const batch = writeBatch(db);

    requestsSnapshot.docs.forEach(doc => {
      const data = doc.data();
      if (data.archivedAt && new Date(data.archivedAt) < oneYearAgo) {
        batch.delete(doc.ref);
        cleanupCount++;
      }
    });

    // Clean up discussions
    const discussionsQuery = query(
      collection(db, 'discussions'),
      where('authorId', '==', userId),
      where('archived', '==', true)
    );
    
    const discussionsSnapshot = await getDocs(discussionsQuery);

    discussionsSnapshot.docs.forEach(doc => {
      const data = doc.data();
      if (data.archivedAt && new Date(data.archivedAt) < oneYearAgo) {
        batch.delete(doc.ref);
        cleanupCount++;
      }
    });

    // Clean up financial goals
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      const goals = userData.financialGoals || [];
      const filteredGoals = goals.filter(goal => {
        if (goal.archived && goal.archivedAt) {
          const archivedDate = new Date(goal.archivedAt);
          if (archivedDate < oneYearAgo) {
            cleanupCount++;
            return false; // Remove this goal
          }
        }
        return true; // Keep this goal
      });

      if (filteredGoals.length !== goals.length) {
        batch.update(userRef, { financialGoals: filteredGoals });
      }
    }

    await batch.commit();

    return {
      success: true,
      cleanupCount,
      message: `Cleaned up ${cleanupCount} old archived items`
    };
  } catch (error) {
    console.error('Error cleaning up old archived items:', error);
    throw error;
  }
}

/**
 * Get cleanup statistics for user
 */
export async function getCleanupStats(userId) {
  try {
    const stats = {
      completedGoalsEligibleForArchiving: 0,
      archivedItems: 0,
      oldArchivedItems: 0
    };

    // Check financial goals
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      const goals = userData.financialGoals || [];
      
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

      goals.forEach(goal => {
        const totalMilestones = goal.milestones.length;
        const completedMilestones = goal.milestones.filter(m => m.completed).length;
        const isFullyCompleted = totalMilestones > 0 && completedMilestones === totalMilestones;

        if (isFullyCompleted && !goal.archived) {
          const completedMilestonesDates = goal.milestones
            .filter(m => m.completed && m.completedAt)
            .map(m => new Date(m.completedAt))
            .sort((a, b) => b - a);

          const lastCompletionDate = completedMilestonesDates[0];
          
          if (lastCompletionDate && lastCompletionDate < thirtyDaysAgo) {
            stats.completedGoalsEligibleForArchiving++;
          }
        }

        if (goal.archived) {
          stats.archivedItems++;
          if (goal.archivedAt && new Date(goal.archivedAt) < oneYearAgo) {
            stats.oldArchivedItems++;
          }
        }
      });
    }

    return stats;
  } catch (error) {
    console.error('Error getting cleanup stats:', error);
    throw error;
  }
}
