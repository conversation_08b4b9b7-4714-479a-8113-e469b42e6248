{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "/service-worker.js", "headers": [{"key": "Cache-Control", "value": "no-cache"}]}]}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true}}}