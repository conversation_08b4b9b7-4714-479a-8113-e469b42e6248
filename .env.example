# Firebase Configuration
# Copy this file to .env.local for local development
# For Netlify production, set these in your Netlify dashboard under Site Settings > Environment Variables
# Get these values from your Firebase project settings

# IMPORTANT: Replace these with your actual Firebase project values
# Current values are for the existing naroop-451d1 project
VITE_FIREBASE_API_KEY=AIzaSyCN6DWF8ecSzAcWLHFzoy0L8vKFnim-fSM
VITE_FIREBASE_AUTH_DOMAIN=naroop-451d1.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=naroop-451d1
VITE_FIREBASE_STORAGE_BUCKET=naroop-451d1.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=280835864133
VITE_FIREBASE_APP_ID=1:280835864133:web:5fcf2953fca215a721152c
VITE_FIREBASE_MEASUREMENT_ID=G-E82HQ5J8T4

# Application Settings
VITE_APP_ENV=development
VITE_APP_VERSION=1.0.0
VITE_APP_NAME=Naroop

# Build Settings (for Netlify)
NODE_ENV=production
NODE_VERSION=18

# Optional: Analytics and Monitoring
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true

# Optional: Feature Flags
VITE_ENABLE_KIDS_SECTION=true
VITE_ENABLE_COMMUNITY_FEATURES=true
VITE_ENABLE_MESSAGING=true
