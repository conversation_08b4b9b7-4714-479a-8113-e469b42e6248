/**
 * Firebase Connection Test Utility
 * Use this to test if Firebase is properly connected and working
 */

import { db } from '../firebase';
import { collection, getDocs, addDoc } from 'firebase/firestore';

export const testFirebaseConnection = async () => {
  try {
    console.log('🔍 Testing Firebase connection...');
    
    // Test 1: Try to read from changelog collection
    const changelogRef = collection(db, 'changelog');
    const snapshot = await getDocs(changelogRef);
    
    console.log('✅ Firebase connection successful!');
    console.log(`📊 Found ${snapshot.docs.length} changelog entries`);
    
    if (snapshot.docs.length === 0) {
      console.log('ℹ️ No changelog entries found - this is why the changelog is empty');
      return {
        success: true,
        connected: true,
        entriesCount: 0,
        message: 'Firebase connected but no changelog entries exist'
      };
    }
    
    const entries = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    console.log('📋 Existing entries:', entries);
    
    return {
      success: true,
      connected: true,
      entriesCount: snapshot.docs.length,
      entries: entries,
      message: 'Firebase connected and entries found'
    };
    
  } catch (error) {
    console.error('❌ Firebase connection failed:', error);
    
    if (error.code === 'permission-denied') {
      return {
        success: false,
        connected: true,
        error: 'Permission denied - check Firebase security rules',
        message: 'Connected but no permission to read changelog'
      };
    }
    
    return {
      success: false,
      connected: false,
      error: error.message,
      message: 'Firebase connection failed'
    };
  }
};

export const testAddChangelogEntry = async () => {
  try {
    console.log('🧪 Testing changelog entry addition...');
    
    const testEntry = {
      version: 'test-' + Date.now(),
      title: 'Test Entry',
      description: 'This is a test entry to verify Firebase write permissions',
      category: 'bugfix',
      releaseDate: new Date().toISOString(),
      changes: ['Test change'],
      isTest: true
    };
    
    const changelogRef = collection(db, 'changelog');
    const docRef = await addDoc(changelogRef, testEntry);
    
    console.log('✅ Test entry added successfully with ID:', docRef.id);
    
    return {
      success: true,
      docId: docRef.id,
      message: 'Write permissions working correctly'
    };
    
  } catch (error) {
    console.error('❌ Failed to add test entry:', error);
    
    return {
      success: false,
      error: error.message,
      message: 'Write permissions failed'
    };
  }
};
