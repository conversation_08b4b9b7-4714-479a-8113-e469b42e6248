/* Guest Prompt Styles */
.guest-prompt {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  margin: 1rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.guest-prompt:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.guest-prompt-content {
  max-width: 400px;
  margin: 0 auto;
}

.guest-prompt-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: bounce 2s ease-in-out infinite;
}

.guest-prompt-title {
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.75rem 0;
  background: linear-gradient(135deg, #d69e2e 0%, #b7791f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.guest-prompt-message {
  color: #4a5568;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.guest-prompt-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.guest-prompt-signup,
.guest-prompt-signin {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
}

.guest-prompt-signup {
  background: linear-gradient(135deg, #d69e2e 0%, #b7791f 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(214, 158, 46, 0.3);
}

.guest-prompt-signup:hover {
  background: linear-gradient(135deg, #b7791f 0%, #975a16 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(214, 158, 46, 0.4);
}

.guest-prompt-signin {
  background: white;
  color: #4a5568;
  border: 2px solid #e2e8f0;
}

.guest-prompt-signin:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

/* Compact variant */
.guest-prompt.compact {
  padding: 1rem;
  margin: 0.5rem 0;
}

.guest-prompt.compact .guest-prompt-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.guest-prompt.compact .guest-prompt-title {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.guest-prompt.compact .guest-prompt-message {
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.guest-prompt.compact .guest-prompt-actions {
  gap: 0.75rem;
}

.guest-prompt.compact .guest-prompt-signup,
.guest-prompt.compact .guest-prompt-signin {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  min-width: 100px;
}

/* Inline variant */
.guest-prompt.inline {
  background: rgba(214, 158, 46, 0.1);
  border: 1px solid rgba(214, 158, 46, 0.3);
  padding: 1rem;
  border-radius: 8px;
  margin: 0.5rem 0;
}

.guest-prompt.inline .guest-prompt-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  text-align: left;
  max-width: none;
}

.guest-prompt.inline .guest-prompt-icon {
  font-size: 1.5rem;
  margin: 0;
  animation: none;
}

.guest-prompt.inline .guest-prompt-title {
  font-size: 1rem;
  margin: 0 0 0.25rem 0;
}

.guest-prompt.inline .guest-prompt-message {
  font-size: 0.85rem;
  margin: 0;
  flex: 1;
}

.guest-prompt.inline .guest-prompt-actions {
  gap: 0.5rem;
  margin: 0;
}

.guest-prompt.inline .guest-prompt-signup,
.guest-prompt.inline .guest-prompt-signin {
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
  min-width: 80px;
}

/* Animations */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .guest-prompt {
    padding: 1.5rem 1rem;
    margin: 0.75rem 0;
  }

  .guest-prompt-title {
    font-size: 1.25rem;
  }

  .guest-prompt-message {
    font-size: 0.9rem;
  }

  .guest-prompt-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .guest-prompt-signup,
  .guest-prompt-signin {
    width: 100%;
    max-width: 200px;
  }

  .guest-prompt.inline .guest-prompt-content {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .guest-prompt.inline .guest-prompt-actions {
    flex-direction: row;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .guest-prompt {
    padding: 1rem 0.75rem;
  }

  .guest-prompt-icon {
    font-size: 2.5rem;
  }

  .guest-prompt-title {
    font-size: 1.1rem;
  }

  .guest-prompt-message {
    font-size: 0.85rem;
  }
}
