# Netlify Deployment Setup for Naroop

## Environment Variables Configuration

To deploy Naroop successfully on Netlify, you need to configure the following environment variables in your Netlify dashboard:

### Required Firebase Environment Variables

Go to your Netlify site dashboard → Site Settings → Environment Variables and add:

```
VITE_FIREBASE_API_KEY=your_actual_api_key
VITE_FIREBASE_AUTH_DOMAIN=naroop-451d1.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=naroop-451d1
VITE_FIREBASE_STORAGE_BUCKET=naroop-451d1.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=280835864133
VITE_FIREBASE_APP_ID=1:280835864133:web:5fcf2953fca215a721152c
VITE_FIREBASE_MEASUREMENT_ID=G-E82HQ5J8T4
```

### Optional Application Variables

```
VITE_APP_ENV=production
VITE_APP_VERSION=1.0.0
VITE_APP_NAME=Naroop
NODE_ENV=production
NODE_VERSION=18
```

### Feature Flags (Optional)

```
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_KIDS_SECTION=true
VITE_ENABLE_COMMUNITY_FEATURES=true
VITE_ENABLE_MESSAGING=true
```

## Build Settings

The `netlify.toml` file in the root directory contains all necessary build settings:

- **Build Command**: `npm run build`
- **Publish Directory**: `dist`
- **Node Version**: 18

## Deployment Workflow

1. **Automatic Deployments**: Every push to the `main` branch will trigger a new deployment
2. **Deploy Previews**: Pull requests will generate preview deployments
3. **Branch Deployments**: Other branches can be configured for branch-specific deployments

## Security Features

The configuration includes:
- Content Security Policy headers
- XSS protection
- Clickjacking prevention
- Optimized caching for static assets

## Performance Optimization

- Bundle splitting for better caching
- Lighthouse performance monitoring
- Optimized asset compression
- Source maps for debugging

## Troubleshooting

### Common Issues:

1. **Build Fails**: Check that all environment variables are set correctly
2. **Firebase Connection Issues**: Verify Firebase configuration values
3. **Routing Issues**: The `_redirects` file handles SPA routing
4. **Performance Issues**: Check Lighthouse reports in deploy logs

### Build Logs

Monitor build logs in Netlify dashboard for any issues during deployment.
