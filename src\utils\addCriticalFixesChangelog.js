/**
 * Utility to add the new v1.1.1 critical fixes changelog entry
 * Run this once to add the new changelog entry to the database
 */

import { addChangelogEntry, CHANGELOG_CATEGORIES } from '../services/changelog.js';

export const addV111ChangelogEntry = async () => {
  try {
    const newEntry = {
      version: '1.1.1',
      title: 'Critical Bug Fixes - Story Submission & Mobile Navigation 🔧',
      description: 'We\'ve fixed several important issues that were affecting your experience on NAROOP. Story sharing now works smoothly, and mobile navigation is much improved!',
      category: CHANGELOG_CATEGORIES.BUGFIX,
      releaseDate: new Date().toISOString(),
      changes: [
        'Fixed story submission errors - you can now share your stories without permission issues',
        'Improved mobile menu - all navigation options are now visible and scrollable on mobile devices',
        'Enhanced form validation with clearer error messages',
        'Better authentication handling to prevent login-related issues',
        'Improved mobile touch targets for easier navigation on phones and tablets',
        'Fixed duplicate Kids Zone display issue for cleaner navigation',
        'Added better error handling throughout the platform',
        'Enhanced security with updated Firebase rules'
      ]
    };

    await addChangelogEntry(newEntry);
    console.log('✅ Successfully added v1.1.1 critical fixes changelog entry!');
    return { success: true, message: 'Critical fixes changelog entry added successfully' };
  } catch (error) {
    console.error('❌ Error adding critical fixes changelog entry:', error);
    return { success: false, error: error.message };
  }
};

// Function to check if the entry already exists
export const checkV111EntryExists = async () => {
  try {
    const { getChangelogByVersion } = await import('../services/changelog.js');
    const entries = await getChangelogByVersion('1.1.1');
    return entries.length > 0;
  } catch (error) {
    console.error('Error checking v1.1.1 entry:', error);
    return false;
  }
};

// Function to add the entry only if it doesn't exist
export const addV111ChangelogIfNeeded = async () => {
  try {
    const exists = await checkV111EntryExists();
    if (exists) {
      console.log('ℹ️ v1.1.1 changelog entry already exists, skipping...');
      return { success: true, message: 'Entry already exists' };
    }
    
    return await addV111ChangelogEntry();
  } catch (error) {
    console.error('❌ Error in addV111ChangelogIfNeeded:', error);
    return { success: false, error: error.message };
  }
};

// Export for use in components
export default addV111ChangelogEntry;
