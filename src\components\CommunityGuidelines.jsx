import React from 'react';
import './CommunityGuidelines.css';

/**
 * Community Guidelines Component
 * Comprehensive guidelines for the NAROOP community
 */
export default function CommunityGuidelines({ onClose }) {
  return (
    <div className="guidelines-modal" onClick={(e) => e.target === e.currentTarget && onClose()}>
      <div className="guidelines-content">
        <div className="guidelines-header">
          <h2>🌟 NAROOP Community Guidelines</h2>
          <p>Building a positive space for African American voices and stories</p>
          <button className="close-guidelines-btn" onClick={onClose}>✕</button>
        </div>

        <div className="guidelines-body">
          <section className="guideline-section">
            <h3>🤝 Our Mission</h3>
            <p>
              NAROOP (Narrative of Our People) is dedicated to creating a safe, empowering, and 
              positive space for the African American community to share stories, connect, and 
              celebrate our rich culture and heritage.
            </p>
          </section>

          <section className="guideline-section">
            <h3>✅ What We Encourage</h3>
            <ul className="guidelines-list positive">
              <li><strong>Authentic Stories:</strong> Share your genuine experiences, struggles, and triumphs</li>
              <li><strong>Cultural Celebration:</strong> Highlight African American culture, traditions, and achievements</li>
              <li><strong>Supportive Community:</strong> Uplift and encourage fellow community members</li>
              <li><strong>Constructive Dialogue:</strong> Engage in respectful discussions about important topics</li>
              <li><strong>Mentorship:</strong> Share knowledge and guidance with others</li>
              <li><strong>Positive Representation:</strong> Counter negative stereotypes with authentic narratives</li>
              <li><strong>Educational Content:</strong> Share historical facts, cultural insights, and learning opportunities</li>
            </ul>
          </section>

          <section className="guideline-section">
            <h3>🚫 What We Don't Allow</h3>
            <ul className="guidelines-list negative">
              <li><strong>Hate Speech:</strong> Any content promoting hatred based on race, religion, gender, or other characteristics</li>
              <li><strong>Harassment:</strong> Bullying, threatening, or intimidating other users</li>
              <li><strong>Discrimination:</strong> Content that discriminates against any group of people</li>
              <li><strong>Misinformation:</strong> Deliberately false or misleading information</li>
              <li><strong>Spam:</strong> Repetitive, irrelevant, or promotional content</li>
              <li><strong>Violence:</strong> Content promoting or glorifying violence</li>
              <li><strong>Inappropriate Content:</strong> Sexual, graphic, or otherwise inappropriate material</li>
              <li><strong>Copyright Violation:</strong> Using others' content without permission</li>
            </ul>
          </section>

          <section className="guideline-section">
            <h3>📝 Content Standards</h3>
            <div className="content-standards">
              <div className="standard-item">
                <h4>Stories & Posts</h4>
                <ul>
                  <li>Must be authentic and original</li>
                  <li>Should contribute positively to the community</li>
                  <li>Must respect others' privacy and dignity</li>
                  <li>Should be appropriate for all ages when possible</li>
                </ul>
              </div>
              <div className="standard-item">
                <h4>Comments & Interactions</h4>
                <ul>
                  <li>Be respectful and constructive</li>
                  <li>Avoid personal attacks or insults</li>
                  <li>Stay on topic and relevant</li>
                  <li>Support fellow community members</li>
                </ul>
              </div>
            </div>
          </section>

          <section className="guideline-section">
            <h3>⚖️ Moderation Process</h3>
            <div className="moderation-process">
              <div className="process-step">
                <div className="step-number">1</div>
                <div className="step-content">
                  <h4>Content Review</h4>
                  <p>All content is reviewed by our moderation team to ensure compliance with guidelines.</p>
                </div>
              </div>
              <div className="process-step">
                <div className="step-number">2</div>
                <div className="step-content">
                  <h4>Community Reports</h4>
                  <p>Community members can report content that violates guidelines for review.</p>
                </div>
              </div>
              <div className="process-step">
                <div className="step-number">3</div>
                <div className="step-content">
                  <h4>Fair Enforcement</h4>
                  <p>Violations are addressed fairly with warnings, suspensions, or bans as appropriate.</p>
                </div>
              </div>
            </div>
          </section>

          <section className="guideline-section">
            <h3>🚨 Reporting Violations</h3>
            <p>If you encounter content that violates these guidelines:</p>
            <ol>
              <li>Use the report button on the content</li>
              <li>Select the appropriate violation type</li>
              <li>Provide specific details about the violation</li>
              <li>Our moderation team will review within 24 hours</li>
            </ol>
          </section>

          <section className="guideline-section">
            <h3>📞 Contact & Appeals</h3>
            <div className="contact-info">
              <p>
                <strong>Questions about guidelines:</strong> Contact our support team<br/>
                <strong>Appeal a moderation decision:</strong> Submit an appeal with details<br/>
                <strong>Urgent safety concerns:</strong> Report immediately for priority review
              </p>
            </div>
          </section>

          <section className="guideline-section">
            <h3>🌱 Growing Together</h3>
            <p>
              These guidelines help us maintain a positive, safe, and empowering community. 
              By participating in NAROOP, you agree to follow these guidelines and help us 
              create a space where African American voices can thrive and be celebrated.
            </p>
            <p className="guidelines-footer">
              <em>Remember: We're here to lift each other up and share the beautiful diversity 
              of our experiences. Together, we can change narratives and create positive representation.</em>
            </p>
          </section>
        </div>

        <div className="guidelines-actions">
          <button className="accept-guidelines-btn" onClick={onClose}>
            ✅ I Understand and Agree
          </button>
        </div>
      </div>
    </div>
  );
}
