import { db } from '../firebase';
import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  serverTimestamp,
  writeBatch,
  onSnapshot
} from 'firebase/firestore';

/**
 * Admin & Moderation Service
 * Handles administrative functions, user roles, and moderation capabilities
 */

// Admin role hierarchy
export const ADMIN_ROLES = {
  PLATFORM_OWNER: 'platform_owner',    // Full access (you)
  SUPER_ADMIN: 'super_admin',          // Nearly full access
  ADMIN: 'admin',                      // Standard admin access
  MODERATOR: 'moderator',              // Content moderation only
  COMMUNITY_HELPER: 'community_helper', // Limited moderation
  USER: 'user'                         // Regular user (default)
};

// Permission levels for different actions
export const PERMISSIONS = {
  // Platform Management
  MANAGE_PLATFORM_SETTINGS: [ADMIN_ROLES.PLATFORM_OWNER],
  VIEW_ANALYTICS: [ADMIN_ROLES.PLATFORM_OWNER, ADMIN_ROLES.SUPER_ADMIN, ADMIN_ROLES.ADMIN],
  MANA<PERSON>_ADMINS: [ADMIN_ROLES.PLATFORM_OWNER],
  
  // User Management
  BAN_USERS: [ADMIN_ROLES.PLATFORM_OWNER, ADMIN_ROLES.SUPER_ADMIN, ADMIN_ROLES.ADMIN],
  SUSPEND_USERS: [ADMIN_ROLES.PLATFORM_OWNER, ADMIN_ROLES.SUPER_ADMIN, ADMIN_ROLES.ADMIN, ADMIN_ROLES.MODERATOR],
  WARN_USERS: [ADMIN_ROLES.PLATFORM_OWNER, ADMIN_ROLES.SUPER_ADMIN, ADMIN_ROLES.ADMIN, ADMIN_ROLES.MODERATOR, ADMIN_ROLES.COMMUNITY_HELPER],
  VIEW_USER_DETAILS: [ADMIN_ROLES.PLATFORM_OWNER, ADMIN_ROLES.SUPER_ADMIN, ADMIN_ROLES.ADMIN, ADMIN_ROLES.MODERATOR],
  
  // Content Moderation
  DELETE_CONTENT: [ADMIN_ROLES.PLATFORM_OWNER, ADMIN_ROLES.SUPER_ADMIN, ADMIN_ROLES.ADMIN, ADMIN_ROLES.MODERATOR],
  APPROVE_CONTENT: [ADMIN_ROLES.PLATFORM_OWNER, ADMIN_ROLES.SUPER_ADMIN, ADMIN_ROLES.ADMIN, ADMIN_ROLES.MODERATOR, ADMIN_ROLES.COMMUNITY_HELPER],
  EDIT_CONTENT: [ADMIN_ROLES.PLATFORM_OWNER, ADMIN_ROLES.SUPER_ADMIN, ADMIN_ROLES.ADMIN],
  VIEW_REPORTED_CONTENT: [ADMIN_ROLES.PLATFORM_OWNER, ADMIN_ROLES.SUPER_ADMIN, ADMIN_ROLES.ADMIN, ADMIN_ROLES.MODERATOR, ADMIN_ROLES.COMMUNITY_HELPER],
  
  // Moderator Management
  ASSIGN_MODERATORS: [ADMIN_ROLES.PLATFORM_OWNER, ADMIN_ROLES.SUPER_ADMIN],
  REMOVE_MODERATORS: [ADMIN_ROLES.PLATFORM_OWNER, ADMIN_ROLES.SUPER_ADMIN],
  VIEW_MODERATOR_ACTIONS: [ADMIN_ROLES.PLATFORM_OWNER, ADMIN_ROLES.SUPER_ADMIN, ADMIN_ROLES.ADMIN]
};

// Platform owner email (you) - this should be your email
const PLATFORM_OWNER_EMAIL = '<EMAIL>'; // Platform owner email

/**
 * Initialize admin system and set platform owner role
 * @param {string} userId - User ID
 * @param {string} email - User email
 */
export async function initializeAdminSystem(userId, email) {
  try {
    console.log('Initializing admin system for:', email);
    console.log('Platform owner email:', PLATFORM_OWNER_EMAIL);
    console.log('Email match:', email === PLATFORM_OWNER_EMAIL);

    // Check if this is the platform owner
    if (email === PLATFORM_OWNER_EMAIL) {
      console.log('Setting platform owner role for:', email);
      await setUserRole(userId, ADMIN_ROLES.PLATFORM_OWNER);
      console.log('Platform owner role assigned successfully to:', email);
    } else {
      console.log('User is not platform owner:', email);
    }

    return { success: true };
  } catch (error) {
    console.error('Error initializing admin system:', error);
    console.error('Error details:', error);
    throw error;
  }
}

/**
 * Get user's admin role and permissions
 * @param {string} userId - User ID
 * @returns {Object} User role and permissions
 */
export async function getUserRole(userId) {
  try {
    const roleDoc = await getDoc(doc(db, 'userRoles', userId));
    
    if (roleDoc.exists()) {
      const data = roleDoc.data();
      return {
        role: data.role,
        assignedBy: data.assignedBy,
        assignedAt: data.assignedAt,
        permissions: getPermissionsForRole(data.role)
      };
    }
    
    // Default to user role
    return {
      role: ADMIN_ROLES.USER,
      permissions: getPermissionsForRole(ADMIN_ROLES.USER)
    };
  } catch (error) {
    console.error('Error getting user role:', error);
    return {
      role: ADMIN_ROLES.USER,
      permissions: getPermissionsForRole(ADMIN_ROLES.USER)
    };
  }
}

/**
 * Set user's admin role
 * @param {string} userId - User ID
 * @param {string} role - Admin role
 * @param {string} assignedBy - ID of admin who assigned the role
 * @returns {Object} Result object
 */
export async function setUserRole(userId, role, assignedBy = null) {
  try {
    console.log('Setting user role:', { userId, role, assignedBy });

    const roleData = {
      userId,
      role,
      assignedBy,
      assignedAt: serverTimestamp(),
      lastUpdated: serverTimestamp()
    };

    console.log('Writing role data to Firestore:', roleData);
    await setDoc(doc(db, 'userRoles', userId), roleData);
    console.log('Role data written successfully');

    // Log the role assignment
    console.log('Logging admin action...');
    await logAdminAction({
      action: 'ROLE_ASSIGNED',
      performedBy: assignedBy || 'SYSTEM',
      targetUserId: userId,
      details: { newRole: role },
      timestamp: serverTimestamp()
    });
    console.log('Admin action logged successfully');

    return { success: true };
  } catch (error) {
    console.error('Error setting user role:', error);
    throw error;
  }
}

/**
 * Check if user has specific permission
 * @param {string} userId - User ID
 * @param {string} permission - Permission to check
 * @returns {boolean} Whether user has permission
 */
export async function hasPermission(userId, permission) {
  try {
    const userRole = await getUserRole(userId);
    const allowedRoles = PERMISSIONS[permission] || [];
    return allowedRoles.includes(userRole.role);
  } catch (error) {
    console.error('Error checking permission:', error);
    return false;
  }
}

/**
 * Get permissions for a specific role
 * @param {string} role - Admin role
 * @returns {Array} Array of permissions
 */
function getPermissionsForRole(role) {
  const permissions = [];
  
  for (const [permission, allowedRoles] of Object.entries(PERMISSIONS)) {
    if (allowedRoles.includes(role)) {
      permissions.push(permission);
    }
  }
  
  return permissions;
}

/**
 * Log admin action for accountability
 * @param {Object} actionData - Action details
 */
export async function logAdminAction(actionData) {
  try {
    const logData = {
      ...actionData,
      id: `${actionData.performedBy}_${Date.now()}`,
      timestamp: actionData.timestamp || serverTimestamp()
    };

    await setDoc(doc(db, 'adminLogs', logData.id), logData);
  } catch (error) {
    console.error('Error logging admin action:', error);
  }
}

/**
 * Get admin action logs
 * @param {Object} filters - Filter options
 * @param {number} limitCount - Number of logs to return
 * @returns {Array} Array of admin logs
 */
export async function getAdminLogs(filters = {}, limitCount = 50) {
  try {
    let logsQuery = query(
      collection(db, 'adminLogs'),
      orderBy('timestamp', 'desc'),
      limit(limitCount)
    );

    // Apply filters
    if (filters.performedBy) {
      logsQuery = query(logsQuery, where('performedBy', '==', filters.performedBy));
    }
    
    if (filters.action) {
      logsQuery = query(logsQuery, where('action', '==', filters.action));
    }

    const snapshot = await getDocs(logsQuery);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
    console.error('Error getting admin logs:', error);
    return [];
  }
}

/**
 * Get all users with admin roles
 * @returns {Array} Array of admin users
 */
export async function getAdminUsers() {
  try {
    const adminRoles = [
      ADMIN_ROLES.PLATFORM_OWNER,
      ADMIN_ROLES.SUPER_ADMIN,
      ADMIN_ROLES.ADMIN,
      ADMIN_ROLES.MODERATOR,
      ADMIN_ROLES.COMMUNITY_HELPER
    ];

    const rolesQuery = query(
      collection(db, 'userRoles'),
      where('role', 'in', adminRoles),
      orderBy('assignedAt', 'desc')
    );

    const snapshot = await getDocs(rolesQuery);
    const adminUsers = [];

    for (const doc of snapshot.docs) {
      const roleData = doc.data();
      
      // Get user details
      const userDoc = await getDoc(doc(db, 'users', roleData.userId));
      if (userDoc.exists()) {
        adminUsers.push({
          ...roleData,
          userInfo: userDoc.data()
        });
      }
    }

    return adminUsers;
  } catch (error) {
    console.error('Error getting admin users:', error);
    return [];
  }
}

/**
 * Remove admin role from user
 * @param {string} userId - User ID
 * @param {string} removedBy - ID of admin removing the role
 * @returns {Object} Result object
 */
export async function removeAdminRole(userId, removedBy) {
  try {
    // Get current role for logging
    const currentRole = await getUserRole(userId);
    
    // Set back to user role
    await setUserRole(userId, ADMIN_ROLES.USER, removedBy);
    
    // Log the role removal
    await logAdminAction({
      action: 'ROLE_REMOVED',
      performedBy: removedBy,
      targetUserId: userId,
      details: { previousRole: currentRole.role },
      timestamp: serverTimestamp()
    });

    return { success: true };
  } catch (error) {
    console.error('Error removing admin role:', error);
    throw error;
  }
}
