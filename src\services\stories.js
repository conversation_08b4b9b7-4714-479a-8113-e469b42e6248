import { db } from '../firebase';
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  query,
  orderBy,
  where,
  limit,
  onSnapshot,
  serverTimestamp,
  arrayUnion,
  arrayRemove,
  getDoc,
  setDoc
} from 'firebase/firestore';
import { submitForModeration } from './moderation';

/**
 * Stories Service
 * Handles all story-related operations with Firebase Firestore
 */

/**
 * Save a new story to Firestore
 */
export async function saveStory(storyData, userId) {
  try {
    // Validate required parameters
    if (!userId) {
      throw new Error('User authentication required. Please log in and try again.');
    }

    if (!storyData || !storyData.title || !storyData.content) {
      throw new Error('Story title and content are required.');
    }

    // Validate user authentication state
    const { auth } = await import('../firebase');
    const currentUser = auth.currentUser;

    if (!currentUser || currentUser.uid !== userId) {
      throw new Error('Authentication mismatch. Please refresh the page and try again.');
    }

    const story = {
      ...storyData,
      author: userId,
      authorEmail: currentUser.email, // Add for debugging
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      hearts: 0,
      claps: 0,
      shares: 0,
      monthlyVotes: 0,
      readingTime: Math.ceil(storyData.content.split(' ').length / 200),
      tags: storyData.tags || [],
      isDraft: false,
      isPublished: false, // Start as unpublished until moderation
      status: 'pending_moderation',
      views: 0,
      comments: [],
      bookmarkedBy: []
    };

    console.log('Attempting to save story for user:', userId);
    const docRef = await addDoc(collection(db, 'stories'), story);
    console.log('Story saved successfully with ID:', docRef.id);

    // Submit for content moderation
    try {
      const moderationResult = await submitForModeration({
        ...story,
        id: docRef.id
      }, 'story');

      // If auto-approved, update the story status
      if (moderationResult.status === 'approved') {
        await updateDoc(doc(db, 'stories', docRef.id), {
          isPublished: true,
          status: 'published',
          publishedAt: serverTimestamp()
        });

        // Update user's story count only for published stories
        await updateUserStoryCount(userId, 1);

        return {
          success: true,
          id: docRef.id,
          message: 'Story published successfully!',
          status: 'published'
        };
      } else {
        return {
          success: true,
          id: docRef.id,
          message: 'Story submitted for review. It will be published once approved by our moderation team.',
          status: 'pending_moderation'
        };
      }
    } catch (moderationError) {
      console.error('Error in moderation process:', moderationError);
      // Still return success for story creation, but note moderation issue
      return {
        success: true,
        id: docRef.id,
        message: 'Story saved but may require manual review.',
        status: 'pending_moderation'
      };
    }
  } catch (error) {
    console.error('Error saving story:', error);

    // Provide more specific error messages
    if (error.code === 'permission-denied') {
      throw new Error('Permission denied. Please check your internet connection and try again.');
    } else if (error.code === 'unauthenticated') {
      throw new Error('Authentication required. Please log out and log back in.');
    } else if (error.message.includes('Missing or insufficient permissions')) {
      throw new Error('Database permissions error. Please contact support if this persists.');
    }

    throw new Error('Failed to save story: ' + error.message);
  }
}

/**
 * Update an existing story
 */
export async function updateStory(storyId, updates, userId) {
  try {
    const storyRef = doc(db, 'stories', storyId);
    const storyDoc = await getDoc(storyRef);
    
    if (!storyDoc.exists()) {
      throw new Error('Story not found');
    }
    
    const storyData = storyDoc.data();
    if (storyData.author !== userId) {
      throw new Error('You can only edit your own stories');
    }
    
    await updateDoc(storyRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
    
    return {
      success: true,
      message: 'Story updated successfully!'
    };
  } catch (error) {
    console.error('Error updating story:', error);
    throw new Error('Failed to update story: ' + error.message);
  }
}

/**
 * Delete a story
 */
export async function deleteStory(storyId, userId) {
  try {
    const storyRef = doc(db, 'stories', storyId);
    const storyDoc = await getDoc(storyRef);
    
    if (!storyDoc.exists()) {
      throw new Error('Story not found');
    }
    
    const storyData = storyDoc.data();
    if (storyData.author !== userId) {
      throw new Error('You can only delete your own stories');
    }
    
    await deleteDoc(storyRef);
    
    // Update user's story count
    await updateUserStoryCount(userId, -1);
    
    return {
      success: true,
      message: 'Story deleted successfully!'
    };
  } catch (error) {
    console.error('Error deleting story:', error);
    throw new Error('Failed to delete story: ' + error.message);
  }
}

/**
 * Get all stories with real-time updates
 */
export function getStoriesRealtime(callback, options = {}) {
  try {
    const {
      limit: queryLimit = 50,
      orderByField = 'createdAt',
      orderDirection = 'desc',
      authorId = null,
      onError = null
    } = options;

    let storiesQuery = query(
      collection(db, 'stories'),
      where('isPublished', '==', true), // Only show published stories
      orderBy(orderByField, orderDirection)
    );

    if (authorId) {
      storiesQuery = query(storiesQuery, where('author', '==', authorId));
    }

    if (queryLimit) {
      storiesQuery = query(storiesQuery, limit(queryLimit));
    }

    return onSnapshot(storiesQuery, (snapshot) => {
      console.log('Stories snapshot received:', snapshot.docs.length, 'stories');
      const stories = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      }))
      // Filter to only show published stories
      .filter(story => story.isPublished !== false);

      console.log('Processed stories (published only):', stories.length);
      callback(stories);
    }, (error) => {
      console.error('Error in stories listener:', error);
      if (onError) {
        onError(error);
      }
      callback([]);
    });
  } catch (error) {
    console.error('Error setting up stories listener:', error);
    if (options.onError) {
      options.onError(error);
    }
    callback([]);
    return () => {}; // Return empty unsubscribe function
  }
}

/**
 * React to a story (heart, clap, share)
 */
export async function reactToStory(storyId, reactionType, userId) {
  try {
    const storyRef = doc(db, 'stories', storyId);
    const storyDoc = await getDoc(storyRef);
    
    if (!storyDoc.exists()) {
      throw new Error('Story not found');
    }
    
    const currentData = storyDoc.data();
    const reactionField = `${reactionType}s`; // hearts, claps, shares
    const userReactionField = `${reactionType}By`; // heartBy, clapBy, shareBy
    
    const currentReactions = currentData[userReactionField] || [];
    const hasReacted = currentReactions.includes(userId);
    
    if (hasReacted) {
      // Remove reaction
      await updateDoc(storyRef, {
        [reactionField]: Math.max(0, (currentData[reactionField] || 0) - 1),
        [userReactionField]: arrayRemove(userId)
      });
    } else {
      // Add reaction
      await updateDoc(storyRef, {
        [reactionField]: (currentData[reactionField] || 0) + 1,
        [userReactionField]: arrayUnion(userId)
      });
      
      // Update author's total reactions
      if (currentData.author !== userId) {
        await updateUserReactionCount(currentData.author, 1);
      }
    }
    
    return {
      success: true,
      hasReacted: !hasReacted,
      newCount: hasReacted 
        ? Math.max(0, (currentData[reactionField] || 0) - 1)
        : (currentData[reactionField] || 0) + 1
    };
  } catch (error) {
    console.error('Error reacting to story:', error);
    throw new Error('Failed to react to story: ' + error.message);
  }
}

/**
 * Vote for story of the month
 */
export async function voteForStoryOfMonth(storyId, userId) {
  try {
    const storyRef = doc(db, 'stories', storyId);
    const storyDoc = await getDoc(storyRef);
    
    if (!storyDoc.exists()) {
      throw new Error('Story not found');
    }
    
    const currentData = storyDoc.data();
    const votedBy = currentData.monthlyVotedBy || [];
    
    if (votedBy.includes(userId)) {
      throw new Error('You have already voted for this story this month');
    }
    
    await updateDoc(storyRef, {
      monthlyVotes: (currentData.monthlyVotes || 0) + 1,
      monthlyVotedBy: arrayUnion(userId)
    });
    
    return {
      success: true,
      newVoteCount: (currentData.monthlyVotes || 0) + 1
    };
  } catch (error) {
    console.error('Error voting for story:', error);
    throw new Error('Failed to vote for story: ' + error.message);
  }
}

/**
 * Bookmark/unbookmark a story
 */
export async function toggleBookmark(storyId, userId) {
  try {
    const storyRef = doc(db, 'stories', storyId);
    const storyDoc = await getDoc(storyRef);
    
    if (!storyDoc.exists()) {
      throw new Error('Story not found');
    }
    
    const currentData = storyDoc.data();
    const bookmarkedBy = currentData.bookmarkedBy || [];
    const isBookmarked = bookmarkedBy.includes(userId);
    
    if (isBookmarked) {
      await updateDoc(storyRef, {
        bookmarkedBy: arrayRemove(userId)
      });
    } else {
      await updateDoc(storyRef, {
        bookmarkedBy: arrayUnion(userId)
      });
    }
    
    return {
      success: true,
      isBookmarked: !isBookmarked
    };
  } catch (error) {
    console.error('Error toggling bookmark:', error);
    throw new Error('Failed to toggle bookmark: ' + error.message);
  }
}

/**
 * Update user's story count
 */
async function updateUserStoryCount(userId, increment) {
  try {
    if (!userId) {
      console.warn('No userId provided for story count update');
      return;
    }

    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      const currentCount = userDoc.data().totalStories || 0;
      await updateDoc(userRef, {
        totalStories: Math.max(0, currentCount + increment),
        lastActivity: new Date().toISOString()
      });
      console.log('User story count updated:', currentCount + increment);
    } else {
      // Create user document if it doesn't exist
      console.log('Creating new user document for story count');
      await setDoc(userRef, {
        totalStories: Math.max(0, increment),
        totalReactions: 0,
        badges: [],
        lastActivity: new Date().toISOString(),
        joinDate: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Error updating user story count:', error);
    // Don't throw error here as it's not critical for story saving
    if (error.code === 'permission-denied') {
      console.warn('Permission denied when updating user story count - this may indicate a security rules issue');
    }
  }
}

/**
 * Update user's reaction count
 */
async function updateUserReactionCount(userId, increment) {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      const currentCount = userDoc.data().totalReactions || 0;
      await updateDoc(userRef, {
        totalReactions: Math.max(0, currentCount + increment)
      });
    }
  } catch (error) {
    console.error('Error updating user reaction count:', error);
  }
}

/**
 * Search stories
 */
export async function searchStories(searchTerm, filters = {}) {
  try {
    let storiesQuery = query(collection(db, 'stories'));
    
    if (filters.author) {
      storiesQuery = query(storiesQuery, where('author', '==', filters.author));
    }
    
    if (filters.topic) {
      storiesQuery = query(storiesQuery, where('topic', '==', filters.topic));
    }
    
    const snapshot = await getDocs(storiesQuery);
    const stories = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    }));
    
    // Filter by search term (client-side for now)
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return stories.filter(story => 
        story.title?.toLowerCase().includes(searchLower) ||
        story.content?.toLowerCase().includes(searchLower) ||
        story.tags?.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }
    
    return stories;
  } catch (error) {
    console.error('Error searching stories:', error);
    throw new Error('Failed to search stories: ' + error.message);
  }
}
