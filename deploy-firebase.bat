@echo off
REM Naroop Firebase Deployment Script for Windows
REM This script deploys Firebase security rules and indexes

echo 🚀 Starting Naroop Firebase Deployment...

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Firebase CLI is not installed. Please install it first:
    echo npm install -g firebase-tools
    exit /b 1
)

REM Check if firebase.json exists
if not exist "firebase.json" (
    echo ❌ firebase.json not found. Please ensure you're in the project root directory.
    exit /b 1
)

REM Check if firestore.rules exists
if not exist "firestore.rules" (
    echo ❌ firestore.rules not found. Please ensure the security rules file exists.
    exit /b 1
)

echo 📋 Deploying Firestore security rules...
firebase deploy --only firestore:rules

if errorlevel 1 (
    echo ❌ Failed to deploy Firestore security rules.
    exit /b 1
)

echo ✅ Firestore security rules deployed successfully!

echo 📊 Deploying Firestore indexes...
firebase deploy --only firestore:indexes

if errorlevel 1 (
    echo ❌ Failed to deploy Firestore indexes.
    exit /b 1
)

echo ✅ Firestore indexes deployed successfully!

echo 🎉 Firebase deployment completed successfully!
echo.
echo 📝 Next steps:
echo 1. Test story submission functionality
echo 2. Verify mobile navigation works correctly
echo 3. Check that all users can access the platform
echo.
echo 🔍 To monitor your Firebase project:
echo firebase console

pause
