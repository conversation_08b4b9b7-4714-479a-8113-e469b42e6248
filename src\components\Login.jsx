import { useRef, useState } from 'react';
import { useAuth } from '../AuthContext';
import naroopLogo from '../assets/naroop-logo.svg';

export default function Login({ onSwitchToSignup, onSwitchToReset, onBackToLanding }) {
  const emailRef = useRef();
  const passwordRef = useRef();
  const { login } = useAuth();
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  async function handleSubmit(e) {
    e.preventDefault();
    setError('');
    setLoading(true);
    try {
      await login(emailRef.current.value, passwordRef.current.value);
    } catch (err) {
      setError('Failed to log in.');
    }
    setLoading(false);
  }

  return (
    <div className="auth-form">
      {onBackToLanding && (
        <button className="back-to-landing" onClick={onBackToLanding}>
          ← Back to Home
        </button>
      )}

      {/* Logo/Brand Section */}
      <div className="auth-brand">
        <img src={naroopLogo} alt="NAROOP Logo" className="auth-logo" />
        <h2>Welcome Back</h2>
        <p className="auth-subtitle">Sign in to continue your journey with NAROOP</p>
      </div>

      {error && <div className="auth-error">{error}</div>}

      <form onSubmit={handleSubmit}>
        <div className="input-group">
          <input
            type="email"
            ref={emailRef}
            placeholder="Email address"
            required
            autoComplete="email"
          />
        </div>
        <div className="input-group">
          <input
            type="password"
            ref={passwordRef}
            placeholder="Password"
            required
            autoComplete="current-password"
          />
        </div>
        <button disabled={loading} type="submit">
          {loading ? 'Signing In...' : 'Sign In'}
        </button>
      </form>

      <div className="auth-links">
        <p>Need an account? <button onClick={onSwitchToSignup}>Sign Up</button></p>
        <p><button onClick={onSwitchToReset}>Forgot Password?</button></p>
      </div>
    </div>
  );
}
