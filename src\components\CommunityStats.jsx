

const DAILY_FACTS = [
  "🏛️ The Great Library of Alexandria was founded by African scholars and housed over 700,000 scrolls.",
  "🎨 <PERSON><PERSON><PERSON> became one of the youngest artists to have work acquired by major museums.",
  "🚀 <PERSON> was the first African American woman astronaut to travel to space in 1992.",
  "📚 The first traffic light was invented by <PERSON>, an African American inventor.",
  "🎵 Jazz, blues, hip-hop, and rock & roll all have their roots in African American culture.",
  "🏥 Dr<PERSON> revolutionized blood storage techniques, saving countless lives.",
  "💡 <PERSON> improved Edison's light bulb design and made electric lighting practical.",
  "🌍 The ancient Kingdom of Kush ruled over Egypt for nearly a century.",
];

export default function CommunityStats({ stories = [] }) {
  const totalStories = stories.length;
  const totalHearts = stories.reduce((sum, story) => sum + (story.hearts || 0), 0);
  const totalClaps = stories.reduce((sum, story) => sum + (story.claps || 0), 0);
  const totalShares = stories.reduce((sum, story) => sum + (story.shares || 0), 0);

  const topTopics = stories.reduce((acc, story) => {
    if (story.topic) {
      acc[story.topic] = (acc[story.topic] || 0) + 1;
    }
    return acc;
  }, {});

  const topTopicsEntries = Object.entries(topTopics);
  const mostPopularTopic = topTopicsEntries.length > 0 ? topTopicsEntries.sort(([,a], [,b]) => b - a)[0] : null;
  
  // Get today's fact based on the date
  const today = new Date();
  const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / (1000 * 60 * 60 * 24));
  const todaysFact = DAILY_FACTS[dayOfYear % DAILY_FACTS.length];
  
  return (
    <section className="naroop-community-stats">
      <div className="naroop-stats-container">
        <div className="naroop-daily-fact">
          <h3>💫 Today's Inspiration</h3>
          <p>{todaysFact}</p>
        </div>
        
        {totalStories > 0 && (
          <div className="naroop-stats-grid">
            <div className="naroop-stat-card">
              <div className="naroop-stat-number">{totalStories}</div>
              <div className="naroop-stat-label">Stories Shared</div>
            </div>
            
            <div className="naroop-stat-card">
              <div className="naroop-stat-number">{totalHearts}</div>
              <div className="naroop-stat-label">Hearts Given</div>
            </div>
            
            <div className="naroop-stat-card">
              <div className="naroop-stat-number">{totalClaps}</div>
              <div className="naroop-stat-label">Applause</div>
            </div>
            
            <div className="naroop-stat-card">
              <div className="naroop-stat-number">{totalShares}</div>
              <div className="naroop-stat-label">Stories Shared</div>
            </div>
            
            {mostPopularTopic && (
              <div className="naroop-stat-card naroop-popular-topic">
                <div className="naroop-stat-label">Most Popular Topic</div>
                <div className="naroop-topic-name">{mostPopularTopic[0]}</div>
                <div className="naroop-topic-count">{mostPopularTopic[1]} stories</div>
              </div>
            )}
          </div>
        )}
      </div>
    </section>
  );
}
