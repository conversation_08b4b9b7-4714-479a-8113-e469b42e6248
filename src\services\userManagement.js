import { db } from '../firebase';
import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { logAdminAction } from './admin';
import { createNotification } from './notifications';

/**
 * User Management Service
 * Handles user suspension, banning, warnings, and safety measures
 */

// User status types
export const USER_STATUS = {
  ACTIVE: 'active',
  WARNED: 'warned',
  SUSPENDED: 'suspended',
  BANNED: 'banned'
};

// Warning types
export const WARNING_TYPES = {
  CONTENT_VIOLATION: 'content_violation',
  COMMUNITY_GUIDELINES: 'community_guidelines',
  HARASSMENT: 'harassment',
  SPAM: 'spam',
  INAPPROPRIATE_BEHAVIOR: 'inappropriate_behavior',
  FINAL_WARNING: 'final_warning'
};

/**
 * Get user's current status and moderation history
 * @param {string} userId - User ID
 * @returns {Object} User status and history
 */
export async function getUserModerationStatus(userId) {
  try {
    const statusDoc = await getDoc(doc(db, 'userModerationStatus', userId));
    
    if (statusDoc.exists()) {
      return statusDoc.data();
    }
    
    // Default status for new users
    return {
      userId,
      status: USER_STATUS.ACTIVE,
      warningCount: 0,
      suspensionCount: 0,
      lastAction: null,
      createdAt: serverTimestamp()
    };
  } catch (error) {
    console.error('Error getting user moderation status:', error);
    return null;
  }
}

/**
 * Issue warning to user
 * @param {string} userId - User ID
 * @param {string} moderatorId - Moderator ID
 * @param {string} warningType - Type of warning
 * @param {string} reason - Warning reason
 * @param {string} details - Additional details
 * @returns {Object} Result object
 */
export async function warnUser(userId, moderatorId, warningType, reason, details = '') {
  try {
    const currentStatus = await getUserModerationStatus(userId);
    const newWarningCount = (currentStatus.warningCount || 0) + 1;
    
    const warningData = {
      id: `warning_${userId}_${Date.now()}`,
      userId,
      moderatorId,
      type: warningType,
      reason,
      details,
      issuedAt: serverTimestamp(),
      warningNumber: newWarningCount
    };

    const batch = writeBatch(db);

    // Add warning record
    batch.set(doc(db, 'userWarnings', warningData.id), warningData);

    // Update user moderation status
    batch.set(doc(db, 'userModerationStatus', userId), {
      ...currentStatus,
      status: newWarningCount >= 3 ? USER_STATUS.WARNED : currentStatus.status,
      warningCount: newWarningCount,
      lastAction: {
        type: 'WARNING',
        reason,
        moderatorId,
        timestamp: serverTimestamp()
      },
      updatedAt: serverTimestamp()
    });

    await batch.commit();

    // Send notification to user
    await createNotification({
      userId,
      type: 'warning',
      title: 'Community Guidelines Warning',
      message: `You have received a warning for: ${reason}. Please review our community guidelines.`,
      actionUrl: '/community-guidelines',
      priority: 'high',
      category: 'moderation'
    });

    // Log the action
    await logAdminAction({
      action: 'USER_WARNED',
      performedBy: moderatorId,
      targetUserId: userId,
      details: { warningType, reason, warningNumber: newWarningCount },
      timestamp: serverTimestamp()
    });

    return { success: true, warningId: warningData.id };
  } catch (error) {
    console.error('Error warning user:', error);
    throw error;
  }
}

/**
 * Suspend user temporarily
 * @param {string} userId - User ID
 * @param {string} moderatorId - Moderator ID
 * @param {number} durationDays - Suspension duration in days
 * @param {string} reason - Suspension reason
 * @param {string} details - Additional details
 * @returns {Object} Result object
 */
export async function suspendUser(userId, moderatorId, durationDays, reason, details = '') {
  try {
    const currentStatus = await getUserModerationStatus(userId);
    const suspensionEnd = new Date();
    suspensionEnd.setDate(suspensionEnd.getDate() + durationDays);

    const suspensionData = {
      id: `suspension_${userId}_${Date.now()}`,
      userId,
      moderatorId,
      reason,
      details,
      durationDays,
      startDate: serverTimestamp(),
      endDate: suspensionEnd,
      isActive: true
    };

    const batch = writeBatch(db);

    // Add suspension record
    batch.set(doc(db, 'userSuspensions', suspensionData.id), suspensionData);

    // Update user moderation status
    batch.set(doc(db, 'userModerationStatus', userId), {
      ...currentStatus,
      status: USER_STATUS.SUSPENDED,
      suspensionCount: (currentStatus.suspensionCount || 0) + 1,
      currentSuspension: suspensionData.id,
      suspensionEnd: suspensionEnd,
      lastAction: {
        type: 'SUSPENSION',
        reason,
        moderatorId,
        durationDays,
        timestamp: serverTimestamp()
      },
      updatedAt: serverTimestamp()
    });

    await batch.commit();

    // Send notification to user
    await createNotification({
      userId,
      type: 'suspension',
      title: 'Account Suspended',
      message: `Your account has been suspended for ${durationDays} days. Reason: ${reason}`,
      actionUrl: '/community-guidelines',
      priority: 'high',
      category: 'moderation'
    });

    // Log the action
    await logAdminAction({
      action: 'USER_SUSPENDED',
      performedBy: moderatorId,
      targetUserId: userId,
      details: { reason, durationDays },
      timestamp: serverTimestamp()
    });

    return { success: true, suspensionId: suspensionData.id };
  } catch (error) {
    console.error('Error suspending user:', error);
    throw error;
  }
}

/**
 * Ban user permanently
 * @param {string} userId - User ID
 * @param {string} moderatorId - Moderator ID
 * @param {string} reason - Ban reason
 * @param {string} details - Additional details
 * @returns {Object} Result object
 */
export async function banUser(userId, moderatorId, reason, details = '') {
  try {
    const currentStatus = await getUserModerationStatus(userId);

    const banData = {
      id: `ban_${userId}_${Date.now()}`,
      userId,
      moderatorId,
      reason,
      details,
      bannedAt: serverTimestamp(),
      isActive: true
    };

    const batch = writeBatch(db);

    // Add ban record
    batch.set(doc(db, 'userBans', banData.id), banData);

    // Update user moderation status
    batch.set(doc(db, 'userModerationStatus', userId), {
      ...currentStatus,
      status: USER_STATUS.BANNED,
      currentBan: banData.id,
      lastAction: {
        type: 'BAN',
        reason,
        moderatorId,
        timestamp: serverTimestamp()
      },
      updatedAt: serverTimestamp()
    });

    await batch.commit();

    // Send notification to user
    await createNotification({
      userId,
      type: 'ban',
      title: 'Account Banned',
      message: `Your account has been permanently banned. Reason: ${reason}`,
      actionUrl: '/appeal',
      priority: 'high',
      category: 'moderation'
    });

    // Log the action
    await logAdminAction({
      action: 'USER_BANNED',
      performedBy: moderatorId,
      targetUserId: userId,
      details: { reason },
      timestamp: serverTimestamp()
    });

    return { success: true, banId: banData.id };
  } catch (error) {
    console.error('Error banning user:', error);
    throw error;
  }
}

/**
 * Lift suspension early
 * @param {string} userId - User ID
 * @param {string} moderatorId - Moderator ID
 * @param {string} reason - Reason for lifting suspension
 * @returns {Object} Result object
 */
export async function liftSuspension(userId, moderatorId, reason = '') {
  try {
    const currentStatus = await getUserModerationStatus(userId);
    
    if (currentStatus.status !== USER_STATUS.SUSPENDED) {
      throw new Error('User is not currently suspended');
    }

    const batch = writeBatch(db);

    // Deactivate current suspension
    if (currentStatus.currentSuspension) {
      batch.update(doc(db, 'userSuspensions', currentStatus.currentSuspension), {
        isActive: false,
        liftedBy: moderatorId,
        liftedAt: serverTimestamp(),
        liftReason: reason
      });
    }

    // Update user status
    batch.update(doc(db, 'userModerationStatus', userId), {
      status: USER_STATUS.ACTIVE,
      currentSuspension: null,
      suspensionEnd: null,
      lastAction: {
        type: 'SUSPENSION_LIFTED',
        reason,
        moderatorId,
        timestamp: serverTimestamp()
      },
      updatedAt: serverTimestamp()
    });

    await batch.commit();

    // Send notification to user
    await createNotification({
      userId,
      type: 'suspension_lifted',
      title: 'Suspension Lifted',
      message: 'Your account suspension has been lifted. Welcome back to NAROOP!',
      actionUrl: '/home',
      priority: 'normal',
      category: 'moderation'
    });

    // Log the action
    await logAdminAction({
      action: 'SUSPENSION_LIFTED',
      performedBy: moderatorId,
      targetUserId: userId,
      details: { reason },
      timestamp: serverTimestamp()
    });

    return { success: true };
  } catch (error) {
    console.error('Error lifting suspension:', error);
    throw error;
  }
}

/**
 * Check if user is currently suspended or banned
 * @param {string} userId - User ID
 * @returns {Object} User access status
 */
export async function checkUserAccess(userId) {
  try {
    const status = await getUserModerationStatus(userId);
    
    if (status.status === USER_STATUS.BANNED) {
      return {
        hasAccess: false,
        reason: 'Account is permanently banned',
        status: USER_STATUS.BANNED
      };
    }
    
    if (status.status === USER_STATUS.SUSPENDED) {
      const now = new Date();
      const suspensionEnd = status.suspensionEnd?.toDate();
      
      if (suspensionEnd && now < suspensionEnd) {
        return {
          hasAccess: false,
          reason: `Account is suspended until ${suspensionEnd.toLocaleDateString()}`,
          status: USER_STATUS.SUSPENDED,
          suspensionEnd
        };
      } else {
        // Suspension has expired, automatically lift it
        await liftSuspension(userId, 'SYSTEM', 'Suspension period expired');
        return {
          hasAccess: true,
          status: USER_STATUS.ACTIVE
        };
      }
    }
    
    return {
      hasAccess: true,
      status: status.status
    };
  } catch (error) {
    console.error('Error checking user access:', error);
    return {
      hasAccess: true,
      status: USER_STATUS.ACTIVE
    };
  }
}

/**
 * Get user's moderation history
 * @param {string} userId - User ID
 * @returns {Object} User's moderation history
 */
export async function getUserModerationHistory(userId) {
  try {
    const [warnings, suspensions, bans] = await Promise.all([
      getDocs(query(collection(db, 'userWarnings'), where('userId', '==', userId), orderBy('issuedAt', 'desc'))),
      getDocs(query(collection(db, 'userSuspensions'), where('userId', '==', userId), orderBy('startDate', 'desc'))),
      getDocs(query(collection(db, 'userBans'), where('userId', '==', userId), orderBy('bannedAt', 'desc')))
    ]);

    return {
      warnings: warnings.docs.map(doc => ({ id: doc.id, ...doc.data() })),
      suspensions: suspensions.docs.map(doc => ({ id: doc.id, ...doc.data() })),
      bans: bans.docs.map(doc => ({ id: doc.id, ...doc.data() }))
    };
  } catch (error) {
    console.error('Error getting user moderation history:', error);
    return { warnings: [], suspensions: [], bans: [] };
  }
}
