import { db } from '../firebase';
import {
  collection,
  addDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  doc,
  updateDoc,
  getDoc,
  getDocs,
  deleteDoc,
  writeBatch,
  serverTimestamp,
  limit
} from 'firebase/firestore';

/**
 * Notification Service
 * Handles real-time notifications, email notifications, and user preferences
 */

/**
 * Create a new notification
 */
export async function createNotification({
  userId,
  type,
  title,
  message,
  actionUrl = null,
  actionData = null,
  priority = 'normal', // 'low', 'normal', 'high', 'urgent'
  category = 'general' // 'message', 'support', 'story', 'campaign', 'system'
}) {
  try {
    const notificationData = {
      userId,
      type,
      title,
      message,
      actionUrl,
      actionData,
      priority,
      category,
      read: false,
      createdAt: serverTimestamp(),
      readAt: null
    };

    const notificationRef = await addDoc(collection(db, 'notifications'), notificationData);

    // Check if user wants email notifications for this type
    const shouldSendEmail = await checkEmailPreference(userId, category, priority);
    if (shouldSendEmail) {
      await queueEmailNotification(userId, notificationData);
    }

    return {
      success: true,
      notificationId: notificationRef.id
    };
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
}

/**
 * Get user notifications with real-time updates
 */
export function getUserNotifications(userId, callback, options = {}) {
  try {
    const {
      limit: queryLimit = 50,
      category = null,
      unreadOnly = false
    } = options;

    let notificationsQuery = query(
      collection(db, 'notifications'),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    if (queryLimit) {
      notificationsQuery = query(notificationsQuery, limit(queryLimit));
    }

    if (category) {
      notificationsQuery = query(notificationsQuery, where('category', '==', category));
    }

    if (unreadOnly) {
      notificationsQuery = query(notificationsQuery, where('read', '==', false));
    }

    return onSnapshot(notificationsQuery, (snapshot) => {
      const notifications = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        readAt: doc.data().readAt?.toDate() || null
      }));

      callback(notifications);
    }, (error) => {
      console.error('Error in notifications listener:', error);
      // Return empty array on error to prevent infinite loading
      callback([]);
    });
  } catch (error) {
    console.error('Error getting notifications:', error);
    // Return empty array on error
    callback([]);
    return () => {}; // Return empty unsubscribe function
  }
}

/**
 * Mark notification as read
 */
export async function markNotificationAsRead(notificationId) {
  try {
    const notificationRef = doc(db, 'notifications', notificationId);
    await updateDoc(notificationRef, {
      read: true,
      readAt: serverTimestamp()
    });

    return { success: true };
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
}

/**
 * Mark all notifications as read for a user
 */
export async function markAllNotificationsAsRead(userId) {
  try {
    const notificationsQuery = query(
      collection(db, 'notifications'),
      where('userId', '==', userId),
      where('read', '==', false)
    );

    const snapshot = await getDocs(notificationsQuery);
    const batch = writeBatch(db);

    snapshot.docs.forEach(doc => {
      batch.update(doc.ref, {
        read: true,
        readAt: serverTimestamp()
      });
    });

    await batch.commit();

    return {
      success: true,
      markedCount: snapshot.docs.length
    };
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
}

/**
 * Delete notification
 */
export async function deleteNotification(notificationId, userId) {
  try {
    const notificationRef = doc(db, 'notifications', notificationId);
    const notificationDoc = await getDoc(notificationRef);

    if (!notificationDoc.exists()) {
      throw new Error('Notification not found');
    }

    const notificationData = notificationDoc.data();
    if (notificationData.userId !== userId) {
      throw new Error('You can only delete your own notifications');
    }

    await deleteDoc(notificationRef);

    return { success: true };
  } catch (error) {
    console.error('Error deleting notification:', error);
    throw error;
  }
}

/**
 * Get unread notification count
 */
export function getUnreadNotificationCount(userId, callback) {
  try {
    const unreadQuery = query(
      collection(db, 'notifications'),
      where('userId', '==', userId),
      where('read', '==', false)
    );

    return onSnapshot(unreadQuery, (snapshot) => {
      callback(snapshot.docs.length);
    }, (error) => {
      console.error('Error in unread count listener:', error);
      // Return 0 on error
      callback(0);
    });
  } catch (error) {
    console.error('Error getting unread count:', error);
    // Return 0 on error
    callback(0);
    return () => {}; // Return empty unsubscribe function
  }
}

/**
 * Update user notification preferences
 */
export async function updateNotificationPreferences(userId, preferences) {
  try {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      notificationPreferences: {
        email: {
          messages: preferences.email?.messages ?? true,
          support: preferences.email?.support ?? true,
          stories: preferences.email?.stories ?? false,
          campaigns: preferences.email?.campaigns ?? true,
          system: preferences.email?.system ?? true
        },
        push: {
          messages: preferences.push?.messages ?? true,
          support: preferences.push?.support ?? true,
          stories: preferences.push?.stories ?? false,
          campaigns: preferences.push?.campaigns ?? false,
          system: preferences.push?.system ?? true
        },
        inApp: {
          messages: preferences.inApp?.messages ?? true,
          support: preferences.inApp?.support ?? true,
          stories: preferences.inApp?.stories ?? true,
          campaigns: preferences.inApp?.campaigns ?? true,
          system: preferences.inApp?.system ?? true
        },
        frequency: preferences.frequency || 'immediate', // 'immediate', 'hourly', 'daily', 'weekly'
        quietHours: {
          enabled: preferences.quietHours?.enabled ?? false,
          start: preferences.quietHours?.start || '22:00',
          end: preferences.quietHours?.end || '08:00'
        }
      }
    });

    return { success: true };
  } catch (error) {
    console.error('Error updating notification preferences:', error);
    throw error;
  }
}

/**
 * Get user notification preferences
 */
export async function getNotificationPreferences(userId) {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      throw new Error('User not found');
    }

    const userData = userDoc.data();
    return userData.notificationPreferences || getDefaultPreferences();
  } catch (error) {
    console.error('Error getting notification preferences:', error);
    throw error;
  }
}

/**
 * Check if user wants email notifications for a specific category and priority
 */
async function checkEmailPreference(userId, category, priority) {
  try {
    const preferences = await getNotificationPreferences(userId);
    
    // Check if email notifications are enabled for this category
    if (!preferences.email[category]) {
      return false;
    }

    // Check quiet hours
    if (preferences.quietHours?.enabled) {
      const now = new Date();
      const currentTime = now.toTimeString().substring(0, 5);
      const { start, end } = preferences.quietHours;
      
      if (start < end) {
        // Same day quiet hours (e.g., 22:00 to 08:00 next day)
        if (currentTime >= start || currentTime <= end) {
          return priority === 'urgent'; // Only urgent notifications during quiet hours
        }
      } else {
        // Overnight quiet hours (e.g., 22:00 to 08:00)
        if (currentTime >= start && currentTime <= end) {
          return priority === 'urgent';
        }
      }
    }

    return true;
  } catch (error) {
    console.error('Error checking email preference:', error);
    return false;
  }
}

/**
 * Queue email notification (placeholder for email service integration)
 */
async function queueEmailNotification(userId, notificationData) {
  try {
    // In a real implementation, this would integrate with an email service
    // like SendGrid, AWS SES, or similar
    console.log('Queuing email notification for user:', userId, notificationData);
    
    // Add to email queue collection
    await addDoc(collection(db, 'emailQueue'), {
      userId,
      ...notificationData,
      status: 'pending',
      attempts: 0,
      queuedAt: serverTimestamp()
    });

    return { success: true };
  } catch (error) {
    console.error('Error queuing email notification:', error);
    throw error;
  }
}

/**
 * Create notification for new message
 */
export async function notifyNewMessage(senderId, receiverId, messagePreview) {
  const senderDoc = await getDoc(doc(db, 'users', senderId));
  const senderName = senderDoc.exists() ? 
    (senderDoc.data().displayName || senderDoc.data().email) : 
    'Someone';

  return createNotification({
    userId: receiverId,
    type: 'new_message',
    title: 'New Message',
    message: `${senderName} sent you a message: "${messagePreview.substring(0, 50)}..."`,
    actionUrl: `/messages/${senderId}`,
    actionData: { senderId, messagePreview },
    priority: 'normal',
    category: 'message'
  });
}

/**
 * Create notification for support request response
 */
export async function notifySupportResponse(requestId, responderId, requestTitle) {
  const responderDoc = await getDoc(doc(db, 'users', responderId));
  const responderName = responderDoc.exists() ? 
    (responderDoc.data().displayName || responderDoc.data().email) : 
    'Someone';

  const requestDoc = await getDoc(doc(db, 'supportRequests', requestId));
  if (!requestDoc.exists()) return;

  const requestData = requestDoc.data();

  return createNotification({
    userId: requestData.authorId,
    type: 'support_response',
    title: 'Support Request Response',
    message: `${responderName} responded to your support request: "${requestTitle}"`,
    actionUrl: `/support/${requestId}`,
    actionData: { requestId, responderId },
    priority: 'normal',
    category: 'support'
  });
}

/**
 * Create notification for story interaction
 */
export async function notifyStoryInteraction(storyId, interactorId, interactionType, storyTitle) {
  const interactorDoc = await getDoc(doc(db, 'users', interactorId));
  const interactorName = interactorDoc.exists() ? 
    (interactorDoc.data().displayName || interactorDoc.data().email) : 
    'Someone';

  const storyDoc = await getDoc(doc(db, 'stories', storyId));
  if (!storyDoc.exists()) return;

  const storyData = storyDoc.data();
  const actionText = interactionType === 'like' ? 'liked' : 'commented on';

  return createNotification({
    userId: storyData.authorId,
    type: 'story_interaction',
    title: 'Story Interaction',
    message: `${interactorName} ${actionText} your story: "${storyTitle}"`,
    actionUrl: `/stories/${storyId}`,
    actionData: { storyId, interactorId, interactionType },
    priority: 'low',
    category: 'story'
  });
}

/**
 * Get default notification preferences
 */
function getDefaultPreferences() {
  return {
    email: {
      messages: true,
      support: true,
      stories: false,
      campaigns: true,
      system: true
    },
    push: {
      messages: true,
      support: true,
      stories: false,
      campaigns: false,
      system: true
    },
    inApp: {
      messages: true,
      support: true,
      stories: true,
      campaigns: true,
      system: true
    },
    frequency: 'immediate',
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00'
    }
  };
}
