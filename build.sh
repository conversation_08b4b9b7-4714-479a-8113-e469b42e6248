#!/bin/bash

# Netlify Build Script for Naroop
# This script ensures proper dependency installation and build process

set -e  # Exit on any error

echo "🚀 Starting Naroop build process..."

# Check Node.js version
echo "📋 Node.js version: $(node --version)"
echo "📋 npm version: $(npm --version)"

# Clean any existing node_modules and package-lock.json to ensure fresh install
echo "🧹 Cleaning existing dependencies..."
rm -rf node_modules package-lock.json

# Install dependencies - this fixes the Rollup optional dependencies bug
echo "📦 Installing dependencies..."
npm install

# Verify Vite installation
echo "🔍 Verifying Vite installation..."
if [ -f "node_modules/.bin/vite" ]; then
    echo "✅ Vite binary found at node_modules/.bin/vite"
elif [ -f "node_modules/vite/bin/vite.js" ]; then
    echo "✅ Vite script found at node_modules/vite/bin/vite.js"
else
    echo "❌ Vite not found! Listing node_modules/vite contents:"
    ls -la node_modules/vite/ || echo "node_modules/vite directory not found"
    exit 1
fi

# Check if npx can find vite
echo "🔍 Testing npx vite..."
npx vite --version || {
    echo "❌ npx vite failed, trying direct execution..."
    node node_modules/vite/bin/vite.js --version || {
        echo "❌ Direct vite execution failed"
        exit 1
    }
}

# Run the build
echo "🏗️ Building application..."
npx vite build || {
    echo "❌ npx vite build failed, trying direct execution..."
    node node_modules/vite/bin/vite.js build || {
        echo "❌ Build failed with both methods"
        exit 1
    }
}

# Verify build output
echo "🔍 Verifying build output..."
if [ -d "dist" ] && [ -f "dist/index.html" ]; then
    echo "✅ Build successful! Output directory created."
    echo "📁 Build artifacts:"
    ls -la dist/
else
    echo "❌ Build output verification failed"
    exit 1
fi

echo "🎉 Build completed successfully!"
