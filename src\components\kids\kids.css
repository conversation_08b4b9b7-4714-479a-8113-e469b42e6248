/* Mobile-First Kids Section Styles - Bright, Colorful, and Child-Friendly */

:root {
  /* Kids-specific touch targets - larger for children */
  --kids-touch-target: 56px;
  --kids-touch-comfortable: 64px;
}

.kids-main {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  font-family: 'Comic Sans MS', cursive, sans-serif;
  padding: 0;
  margin: 0;
}

/* Mobile-First Kids Header */
.kids-header {
  background: linear-gradient(45deg, #50C878, #FFD700, #4B0082, #B8860B, #DC143C);
  background-size: 300% 300%;
  animation: rainbow 3s ease infinite;
  padding: 1rem 0.5rem;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

@keyframes rainbow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.kids-header-content {
  /* Mobile-first header layout */
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  flex-direction: column;
  gap: 1rem;
  padding: 0 1rem;
}

.kids-back-btn {
  background: #fff;
  color: #333;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: bold;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.2s;
  min-height: var(--kids-touch-target);
  min-width: var(--kids-touch-target);
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: flex-start;
}

.kids-back-btn:hover {
  transform: scale(1.05);
}

.kids-logo h1 {
  color: #fff;
  text-align: center;
  margin: 0;
  font-size: 2.5rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.kids-logo p {
  color: #fff;
  text-align: center;
  margin: 0.5rem 0 0 0;
  font-size: 1.2rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.parental-controls-btn {
  background: #fff;
  border: none;
  padding: 0.5rem;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.parental-controls-btn:hover {
  transform: scale(1.1);
}

/* Kids Navigation */
.kids-nav {
  background: #fff;
  padding: 1rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  flex-wrap: wrap;
}

.kids-nav-btn {
  background: linear-gradient(45deg, #50C878, #FFD700);
  color: #fff;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: bold;
  font-size: 1.1rem;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.kids-nav-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.kids-nav-btn.active {
  background: linear-gradient(45deg, #FFD700, #4B0082);
  transform: scale(1.05);
}

/* Parental Controls Panel */
.parental-controls-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.parental-controls-content {
  background: #fff;
  padding: 2rem;
  border-radius: 15px;
  max-width: 500px;
  margin: 1rem;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.safety-features {
  margin: 1rem 0;
}

.safety-item {
  margin: 0.5rem 0;
  color: #27ae60;
  font-weight: bold;
}

/* Kids Content */
.kids-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Kids Home */
.kids-welcome {
  text-align: center;
  background: #fff;
  padding: 2rem;
  border-radius: 20px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.kids-welcome h2 {
  color: #ff6b6b;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.kids-welcome p {
  font-size: 1.3rem;
  color: #333;
  line-height: 1.6;
}

.kids-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.kids-feature-card {
  background: #fff;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  transition: transform 0.3s;
}

.kids-feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.kids-feature-card h3 {
  color: #4ecdc4;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.kids-feature-card p {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.feature-btn {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  color: #fff;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: bold;
  transition: transform 0.2s;
  display: inline-block;
}

.feature-btn:hover {
  transform: scale(1.05);
}

.kids-daily-message {
  text-align: center;
}

.daily-message-card {
  background: linear-gradient(45deg, #feca57, #ff9ff3);
  color: #fff;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.daily-message-card h3 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
}

.daily-message-card p {
  font-size: 1.3rem;
  line-height: 1.6;
}

/* Kids Footer */
.kids-footer {
  background: #333;
  color: #fff;
  text-align: center;
  padding: 2rem;
  margin-top: 3rem;
}

.kids-footer p {
  font-size: 1.2rem;
  margin: 0;
}

/* Kids Stories Styles */
.kids-stories {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.stories-header {
  text-align: center;
  margin-bottom: 2rem;
}

.stories-header h2 {
  color: #ff6b6b;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.stories-filters {
  display: flex;
  gap: 2rem;
  justify-content: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: bold;
  color: #333;
}

.filter-group select {
  padding: 0.5rem 1rem;
  border-radius: 15px;
  border: 2px solid #4ecdc4;
  background: #fff;
  font-size: 1rem;
  cursor: pointer;
}

.stories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.story-card {
  background: #fff;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  transition: transform 0.3s;
}

.story-card:hover {
  transform: translateY(-5px);
}

.story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.story-emoji {
  font-size: 3rem;
}

.story-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.story-age, .story-time {
  background: #feca57;
  color: #333;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: bold;
}

.story-card h3 {
  color: #4ecdc4;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.story-category {
  color: #ff6b6b;
  font-weight: bold;
  margin-bottom: 1rem;
}

.story-content {
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.story-lesson {
  background: linear-gradient(45deg, #feca57, #ff9ff3);
  color: #fff;
  padding: 1rem;
  border-radius: 15px;
}

.story-lesson h4 {
  margin-bottom: 0.5rem;
}

.stories-encouragement {
  text-align: center;
  background: linear-gradient(45deg, #96ceb4, #a8e6cf);
  color: #fff;
  padding: 2rem;
  border-radius: 20px;
  margin-top: 2rem;
}

.no-stories {
  text-align: center;
  padding: 3rem;
  color: #666;
}

/* Kids Games Styles */
.kids-games {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.games-header {
  text-align: center;
  margin-bottom: 2rem;
}

.games-header h2 {
  color: #ff6b6b;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.game-selector {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.game-btn {
  background: linear-gradient(45deg, #4ecdc4, #96ceb4);
  color: #fff;
  padding: 1rem 2rem;
  border: none;
  border-radius: 25px;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
}

.game-btn:hover {
  transform: scale(1.05);
}

.game-btn.active {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
}

/* Quiz Game Styles */
.quiz-game {
  background: #fff;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  max-width: 600px;
  margin: 0 auto;
}

.question-progress {
  text-align: center;
  color: #666;
  margin-bottom: 1rem;
  font-weight: bold;
}

.quiz-question h3 {
  color: #4ecdc4;
  font-size: 1.5rem;
  margin-bottom: 2rem;
  text-align: center;
}

.quiz-options {
  display: grid;
  gap: 1rem;
}

.quiz-option {
  background: #f8f9fa;
  border: 2px solid #4ecdc4;
  padding: 1rem;
  border-radius: 15px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.quiz-option:hover {
  background: #4ecdc4;
  color: #fff;
}

.quiz-result {
  text-align: center;
}

.quiz-result h3 {
  color: #ff6b6b;
  font-size: 2rem;
  margin-bottom: 1rem;
}

.result-message {
  font-size: 1.2rem;
  margin: 1rem 0;
  font-weight: bold;
}

.play-again-btn {
  background: linear-gradient(45deg, #feca57, #ff9ff3);
  color: #fff;
  padding: 1rem 2rem;
  border: none;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s;
}

.play-again-btn:hover {
  transform: scale(1.05);
}

/* Tablet Kids Styles */
@media screen and (min-width: 768px) {
  .kids-header {
    padding: 1rem;
  }

  .kids-header-content {
    flex-direction: row;
    gap: 2rem;
    padding: 0 2rem;
  }

  .kids-logo h1 {
    font-size: 2.5rem;
  }

  .kids-nav {
    flex-direction: row;
    justify-content: center;
  }

  .kids-features {
    grid-template-columns: repeat(2, 1fr);
  }

  .kids-welcome h2 {
    font-size: 2.5rem;
  }

  .stories-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Desktop Kids Styles */
@media screen and (min-width: 1024px) {
  .kids-features {
    grid-template-columns: repeat(4, 1fr);
  }

  .kids-welcome h2 {
    font-size: 3rem;
  }

  .stories-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Memory Game Styles */
.memory-game {
  background: #fff;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  max-width: 800px;
  margin: 0 auto;
}

.memory-info {
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
}

.memory-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  max-width: 400px;
  margin: 0 auto;
}

.memory-card {
  aspect-ratio: 1;
  background: #4ecdc4;
  border-radius: 15px;
  cursor: pointer;
  position: relative;
  transition: transform 0.3s;
}

.memory-card:hover {
  transform: scale(1.05);
}

.card-front, .card-back {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  border-radius: 15px;
  transition: transform 0.3s;
}

.card-front {
  background: #4ecdc4;
  color: #fff;
}

.card-back {
  background: #fff;
  color: #333;
  transform: rotateY(180deg);
}

.memory-card.flipped .card-front {
  transform: rotateY(180deg);
}

.memory-card.flipped .card-back {
  transform: rotateY(0deg);
}

.memory-complete {
  text-align: center;
  margin-top: 2rem;
}

/* Coloring Game Styles */
.coloring-game {
  background: #fff;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.coloring-canvas {
  margin-top: 2rem;
}

.color-shapes {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.color-shape {
  width: 80px;
  height: 80px;
  cursor: pointer;
  transition: transform 0.3s;
  border-radius: 10px;
}

.color-shape:hover {
  transform: scale(1.1);
}

.color-shape.circle {
  border-radius: 50%;
}

.color-shape.triangle {
  width: 0;
  height: 0;
  border-left: 40px solid transparent;
  border-right: 40px solid transparent;
  border-bottom: 80px solid;
  background: none !important;
}

.color-shape.star {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: #fff;
}

/* Kids Learning Styles */
.kids-learning {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.learning-header {
  text-align: center;
  margin-bottom: 2rem;
}

.learning-header h2 {
  color: #ff6b6b;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.topic-selector {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.topic-btn {
  background: linear-gradient(45deg, #4ecdc4, #96ceb4);
  color: #fff;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
}

.topic-btn:hover {
  transform: scale(1.05);
}

.topic-btn.active {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
}

.learning-content {
  background: #fff;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.learning-content h3 {
  color: #4ecdc4;
  font-size: 2rem;
  text-align: center;
  margin-bottom: 2rem;
}

.learning-section {
  margin-bottom: 3rem;
}

.learning-section h4 {
  color: #ff6b6b;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.section-description {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.facts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.fact-card {
  background: linear-gradient(45deg, #feca57, #ff9ff3);
  color: #fff;
  padding: 1rem;
  border-radius: 15px;
  font-weight: bold;
}

.learning-activity {
  background: #fff;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.learning-activity h3 {
  color: #4ecdc4;
  text-align: center;
  margin-bottom: 1rem;
}

.learning-encouragement {
  text-align: center;
  background: linear-gradient(45deg, #96ceb4, #a8e6cf);
  color: #fff;
  padding: 2rem;
  border-radius: 20px;
}

/* Kids Heroes Styles */
.kids-heroes {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.heroes-header {
  text-align: center;
  margin-bottom: 2rem;
}

.heroes-header h2 {
  color: #ff6b6b;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.heroes-filter {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.heroes-filter label {
  font-weight: bold;
  color: #333;
}

.heroes-filter select {
  padding: 0.5rem 1rem;
  border-radius: 15px;
  border: 2px solid #4ecdc4;
  background: #fff;
  font-size: 1rem;
  cursor: pointer;
}

.heroes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.hero-card {
  background: #fff;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s;
}

.hero-card:hover {
  transform: translateY(-5px);
}

.hero-emoji {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.hero-card h3 {
  color: #4ecdc4;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.hero-category {
  color: #ff6b6b;
  font-weight: bold;
  margin-bottom: 1rem;
}

.hero-achievement {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.learn-more-btn {
  background: linear-gradient(45deg, #feca57, #ff9ff3);
  color: #fff;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s;
}

.learn-more-btn:hover {
  transform: scale(1.05);
}

/* Hero Modal Styles */
.hero-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.hero-modal-content {
  background: #fff;
  border-radius: 20px;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.close-modal {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #ff6b6b;
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 1001;
}

.hero-detail {
  padding: 2rem;
}

.hero-header-detail {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.hero-emoji-large {
  font-size: 5rem;
}

.hero-header-detail h2 {
  color: #4ecdc4;
  font-size: 2rem;
  margin: 0;
}

.hero-category-detail {
  color: #ff6b6b;
  font-weight: bold;
}

.hero-achievement-detail,
.hero-story-detail,
.hero-lesson-detail,
.hero-funfact-detail,
.hero-inspiration-detail {
  margin-bottom: 2rem;
}

.hero-detail h3 {
  color: #333;
  margin-bottom: 1rem;
}

.hero-detail p {
  line-height: 1.6;
  color: #666;
}

/* Heroes Inspiration Section */
.heroes-inspiration {
  background: #fff;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.heroes-inspiration h3 {
  color: #4ecdc4;
  text-align: center;
  font-size: 2rem;
  margin-bottom: 2rem;
}

.inspiration-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.inspiration-card {
  background: linear-gradient(45deg, #feca57, #ff9ff3);
  color: #fff;
  padding: 1.5rem;
  border-radius: 15px;
  text-align: center;
}

.inspiration-card h4 {
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.heroes-cta {
  text-align: center;
  background: linear-gradient(45deg, #96ceb4, #a8e6cf);
  color: #fff;
  padding: 2rem;
  border-radius: 20px;
}

.heroes-cta h3 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
}

/* Additional responsive styles for kids section */
@media (max-width: 768px) {
  .memory-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .color-shapes {
    gap: 1rem;
  }

  .color-shape {
    width: 60px;
    height: 60px;
  }

  .topic-selector {
    flex-direction: column;
    align-items: center;
  }

  .facts-grid {
    grid-template-columns: 1fr;
  }

  .heroes-grid {
    grid-template-columns: 1fr;
  }

  .inspiration-cards {
    grid-template-columns: 1fr;
  }

  .hero-header-detail {
    flex-direction: column;
    text-align: center;
  }
}

/* Safety and COPPA Compliance Styles */
.coppa-compliance {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 1rem;
}

.coppa-content {
  background: #fff;
  padding: 2rem;
  border-radius: 20px;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.coppa-content h3 {
  color: #4ecdc4;
  text-align: center;
  margin-bottom: 1rem;
}

.coppa-highlights {
  margin: 1.5rem 0;
}

.coppa-item {
  margin: 0.5rem 0;
  color: #27ae60;
  font-weight: bold;
}

.coppa-details {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 15px;
  margin: 1rem 0;
}

.coppa-details h4 {
  color: #333;
  margin-bottom: 1rem;
}

.coppa-details ul {
  margin-left: 1rem;
}

.coppa-details li {
  margin: 0.5rem 0;
  line-height: 1.5;
}

.coppa-actions {
  text-align: center;
  margin-top: 2rem;
}

.coppa-details-btn {
  background: #6c757d;
  color: #fff;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  margin-bottom: 1rem;
}

.coppa-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.coppa-accept {
  background: #27ae60;
  color: #fff;
  padding: 1rem 2rem;
  border: none;
  border-radius: 25px;
  font-weight: bold;
  cursor: pointer;
}

.coppa-decline {
  background: #e74c3c;
  color: #fff;
  padding: 1rem 2rem;
  border: none;
  border-radius: 25px;
  font-weight: bold;
  cursor: pointer;
}

/* Parental Controls Styles */
.parental-controls-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 1rem;
}

.parental-controls-panel {
  background: #fff;
  border-radius: 20px;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
}

.controls-header {
  background: linear-gradient(45deg, #4ecdc4, #96ceb4);
  color: #fff;
  padding: 1.5rem;
  border-radius: 20px 20px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-controls {
  background: none;
  border: none;
  color: #fff;
  font-size: 2rem;
  cursor: pointer;
}

.controls-content {
  padding: 2rem;
}

.control-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.control-section:last-child {
  border-bottom: none;
}

.control-section h4 {
  color: #333;
  margin-bottom: 1rem;
}

.time-selector select {
  padding: 0.5rem;
  border-radius: 10px;
  border: 2px solid #4ecdc4;
  margin-left: 1rem;
}

.content-selector {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.content-selector label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.safety-info {
  display: grid;
  gap: 0.5rem;
}

.safety-item {
  color: #27ae60;
  font-weight: bold;
}

.contact-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 10px;
  margin-top: 1rem;
}

.contact-info div {
  margin: 0.5rem 0;
}

.controls-footer {
  padding: 1.5rem;
  text-align: center;
  border-top: 1px solid #eee;
}

.save-settings {
  background: linear-gradient(45deg, #27ae60, #2ecc71);
  color: #fff;
  padding: 1rem 2rem;
  border: none;
  border-radius: 25px;
  font-weight: bold;
  cursor: pointer;
  font-size: 1.1rem;
}

/* Age Verification Styles */
.age-verification {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 1rem;
}

.verification-content {
  background: #fff;
  padding: 2rem;
  border-radius: 20px;
  max-width: 400px;
  text-align: center;
}

.verification-content h3 {
  color: #4ecdc4;
  margin-bottom: 1rem;
}

.verification-form {
  margin-top: 1.5rem;
}

.verification-form label {
  display: block;
  margin-bottom: 1rem;
  text-align: left;
}

.verification-form input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #4ecdc4;
  border-radius: 10px;
  margin-top: 0.5rem;
  font-size: 1rem;
}

.verification-error {
  color: #e74c3c;
  margin: 1rem 0;
  font-weight: bold;
}

.verification-form button {
  background: linear-gradient(45deg, #4ecdc4, #96ceb4);
  color: #fff;
  padding: 1rem 2rem;
  border: none;
  border-radius: 25px;
  font-weight: bold;
  cursor: pointer;
  font-size: 1.1rem;
  margin-top: 1rem;
}
