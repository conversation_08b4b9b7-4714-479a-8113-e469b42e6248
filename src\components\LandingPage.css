/* Landing Page Styles */
:root {
  /* Consistent NAROOP Color Palette */

  /* Primary Foundation Colors */
  --color-primary-cream: #FDFBF5; /* Light cream background for all sections */
  --color-text-primary: #591C28; /* Dark maroon for headlines and primary text */
  --color-text-secondary: #6E8C65; /* Muted green for secondary text and accents */
  --color-accent-highlight: #F7D046; /* Warm yellow for highlights and primary button hovers */

  /* Button and Interactive Element Colors */
  --color-button-bg-default: rgba(89, 28, 40, 0.1); /* Translucent maroon for default button backgrounds */
  --color-button-border: #591C28; /* Dark maroon for button borders */
  --color-button-hover-primary: #F7D046; /* Warm yellow fill for primary action buttons on hover */
  --color-button-hover-secondary: #6E8C65; /* Muted green fill for secondary buttons on hover */

  /* Card and Container Colors */
  --color-card-bg: #FFFFFF; /* White background for content cards */
  --color-card-shadow: rgb(0 0 0 / 0.1); /* Subtle shadow for cards */

  /* Spacing and Layout - Enhanced for better visual hierarchy */
  --space-xs: 0.5rem;
  --space-sm: 0.75rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 5rem;
  --space-5xl: 6rem;
  --space-6xl: 8rem;

  /* Typography */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease;
}

/* Global Button Pill Shape - Applied to all buttons throughout NAROOP */
button:not(.custom-shape) {
  border-radius: 9999px !important; /* Perfect pill shape for all buttons */
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all var(--transition-normal) !important;
  font-weight: 600 !important;
  border: 2px solid var(--color-text-secondary) !important;
  min-height: 44px !important; /* Optimal height for pill shape */
  padding: 12px 24px !important; /* Fixed padding for perfect pill shape */
  background-color: transparent !important;
  color: var(--color-text-secondary) !important;
}

/* Small button variant */
button.btn-small:not(.custom-shape) {
  padding: 8px 16px !important;
  min-height: 32px !important;
  font-size: 0.9rem !important;
}

/* Large button variant */
button.btn-large:not(.custom-shape) {
  padding: 20px 40px !important;
  min-height: 60px !important;
  font-size: 1.2rem !important;
}

.landing-page {
  min-height: 100vh;
  background: var(--color-primary-cream);
  color: var(--color-text-primary);
  overflow-x: hidden;
  position: relative;
}

/* Header */
.landing-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: rgba(253, 251, 245, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(89, 28, 40, 0.15);
  transition: all var(--transition-normal);
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-md) var(--space-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-logo {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.header-logo .logo-image {
  height: 40px;
  width: 40px;
  object-fit: contain;
}

.header-logo .logo-text {
  font-size: var(--text-xl);
  font-weight: 900;
  color: var(--color-text-primary);
  text-shadow: none;
  letter-spacing: -0.02em;
}

.header-signin {
  background: transparent;
  border: 2px solid var(--color-text-secondary);
  color: var(--color-text-secondary);
  padding: 12px 24px; /* Fixed padding for perfect pill shape */
  border-radius: 9999px; /* Perfect pill shape */
  font-weight: 700;
  font-size: var(--text-base);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(110, 140, 101, 0.15);
  min-height: 44px; /* Optimal height for pill shape */
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.header-signin:hover {
  background: var(--color-button-hover-primary);
  color: var(--color-text-primary);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(247, 208, 70, 0.35);
  border-color: var(--color-accent-highlight);
}

/* Section spacing and transitions - Enhanced */
.landing-page section {
  position: relative;
}

.landing-page section:not(:last-child) {
  margin-bottom: var(--space-5xl);
}

/* Hero Section - Better centered and spaced */
.landing-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calc(var(--space-4xl) + 80px) var(--space-2xl) var(--space-4xl);
  position: relative;
  background: var(--color-primary-cream);
}

/* Professional section spacing and separators - Enhanced */
.landing-page section:not(.landing-hero):not(:last-child) {
  margin-bottom: var(--space-6xl);
  padding-bottom: var(--space-3xl);
}

/* Professional minimalistic section separators - Enhanced */
.landing-page section:not(.landing-hero):not(.landing-footer)::after {
  content: '';
  position: absolute;
  bottom: var(--space-xl);
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(89, 28, 40, 0.3), transparent);
}

/* Remove hero background overlay for clean design */

/* Hero content - Better spacing and centering */
.hero-content {
  max-width: 1400px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4xl);
  align-items: center;
  margin: 0 auto;
}

.hero-text {
  z-index: 2;
  text-align: left;
}

.hero-title {
  margin: 0 0 var(--space-2xl) 0;
}

.brand-name {
  display: block;
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 900;
  letter-spacing: -0.02em;
  color: var(--color-text-primary);
  margin-bottom: var(--space-lg);
  text-shadow: none;
  line-height: 0.9;
}

.brand-subtitle {
  display: block;
  font-size: clamp(1.2rem, 3vw, 2rem);
  font-weight: 500;
  color: var(--color-text-secondary);
  text-shadow: none;
  font-style: italic;
  line-height: 1.2;
}

.hero-description {
  font-size: var(--text-lg);
  line-height: 1.8;
  margin: 0 0 var(--space-3xl) 0;
  color: var(--color-text-secondary);
  text-shadow: none;
  max-width: 650px;
}

/* Hero actions removed - Sign In moved to header */

.cta-primary, .cta-secondary {
  padding: 16px 32px; /* Fixed padding for perfect pill shape */
  border-radius: 9999px; /* Perfect pill shape */
  font-size: var(--text-lg);
  font-weight: 700;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 2px solid var(--color-text-secondary);
  min-height: 52px; /* Optimal height for pill shape */
  min-width: 160px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  color: var(--color-text-secondary);
}

.cta-primary {
  /* Primary action buttons - fill with warm yellow on hover */
  border: 2px solid var(--color-text-secondary);
}

.cta-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(247, 208, 70, 0.35);
  background: var(--color-button-hover-primary);
  color: var(--color-text-primary);
  border-color: var(--color-accent-highlight);
}

.cta-secondary {
  /* Secondary/informational buttons - fill with muted green on hover */
  border: 2px solid var(--color-text-secondary);
}

.cta-secondary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(110, 140, 101, 0.35);
  background: var(--color-button-hover-secondary);
  color: var(--color-primary-cream);
  border-color: var(--color-text-secondary);
}



.hero-visual {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
}

.hero-visual::before {
  content: '';
  position: absolute;
  top: -2rem;
  right: -2rem;
  width: 80px;
  height: 80px;
  background: var(--color-accent-highlight);
  border-radius: 15px; /* Square with rounded corners */
  transform: rotate(45deg);
  opacity: 0.12;
  animation: float 6s ease-in-out infinite;
}

.hero-visual::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: -1rem;
  width: 60px;
  height: 60px;
  background: var(--color-text-secondary);
  border-radius: 50px; /* Pill-shaped narrow icon */
  opacity: 0.10;
  animation: float 4s ease-in-out infinite reverse;
}

.hero-icon {
  font-size: clamp(4rem, 10vw, 8rem);
  margin-bottom: var(--space-lg);
  animation: float 3s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.3));
  transition: filter var(--transition-normal);
  cursor: default;
}

.hero-icon:hover {
  filter: drop-shadow(0 0 30px rgba(255, 215, 0, 0.5));
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.hero-tagline {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--color-text-primary);
  letter-spacing: 2px;
  text-transform: uppercase;
  text-shadow: none;
  background: rgba(253, 251, 245, 0.9);
  padding: var(--space-sm) var(--space-md);
  border-radius: 9999px; /* Pill-shaped narrow element */
  backdrop-filter: blur(10px);
  border: 1px solid rgba(89, 28, 40, 0.15);
}

/* Scroll Indicator - Enhanced spacing */
.scroll-indicator {
  position: absolute;
  bottom: 3rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none !important;
  box-shadow: none !important;
}

.scroll-text {
  font-size: 1.2rem;
  color: var(--color-text-secondary);
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: uppercase;
  background: rgba(255, 255, 255, 0.95);
  padding: 0.6em 2em;
  border-radius: 9999px;
  border: 2px solid var(--color-button-border);
  box-shadow: 0 4px 16px rgba(89,28,40,0.12);
  margin-bottom: 0.2em;
  outline: none;
  display: inline-block;
  backdrop-filter: blur(10px);
}

.scroll-chevron {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  box-shadow: none;
  margin-top: 0;
}

.scroll-chevron::after {
  content: '';
  display: block;
  width: 16px;
  height: 16px;
  border-right: 3px solid var(--color-button-border);
  border-bottom: 3px solid var(--color-button-border);
  transform: rotate(45deg);
  margin: 0 auto;
}

/* Remove extra borders/shadows from scroll-indicator and children */
.scroll-indicator,
.scroll-indicator * {
  box-shadow: none !important;
  border-width: 0 !important;
  background: none !important;
}

.scroll-text {
  border-width: 2px !important;
  border-style: solid !important;
  border-color: var(--color-button-border) !important;
  background: #fff !important;
  color: var(--color-text-secondary) !important;
}

.scroll-indicator:focus-visible .scroll-text {
  outline: 3px solid var(--color-accent-highlight);
  outline-offset: 2px;
}

/* Features Section - Enhanced spacing and centering */
.landing-features {
  background: var(--color-primary-cream);
  color: var(--color-text-primary);
  padding: var(--space-4xl) var(--space-2xl);
  position: relative;
}

.landing-features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--color-accent-highlight);
}

.features-container {
  max-width: 1400px;
  margin: 0 auto;
  text-align: center;
}

.features-container h2 {
  font-size: var(--text-4xl);
  font-weight: 700;
  margin: 0 0 var(--space-2xl) 0;
  color: var(--color-text-primary);
  line-height: 1.2;
}

.features-container::after {
  content: '';
  display: block;
  width: 80px;
  height: 4px;
  background: var(--color-accent-highlight);
  margin: var(--space-2xl) auto var(--space-4xl) auto;
  border-radius: 2px;
}

/* Feature cards - Enhanced spacing and layout */
.features-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-2xl);
}

.feature-card {
  background: var(--color-card-bg);
  padding: var(--space-2xl);
  border-radius: 1.5rem;
  text-align: left;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  border: 2px solid var(--color-button-border);
  box-shadow: 0 6px 24px rgba(89, 28, 40, 0.12);
  margin-bottom: var(--space-2xl);
  display: flex;
  align-items: flex-start;
  gap: var(--space-2xl);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(89, 28, 40, 0.18);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 0;
  margin-right: var(--space-lg);
  color: var(--color-text-secondary);
  background: var(--color-accent-highlight, #F7D046);
  border-radius: 50%;
  border: 3px solid var(--color-button-border);
  padding: 0.8em;
  min-width: 60px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(110, 140, 101, 0.15);
  flex-shrink: 0;
}

.feature-card h3 {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0 0 var(--space-md) 0;
  line-height: 1.3;
}

.feature-card p {
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin: 0;
  font-weight: 400;
  font-size: var(--text-base);
}

.features-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

/* Responsive grid for features - Better spacing */
@media (min-width: 700px) {
  .features-grid {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--space-2xl);
  }
  .feature-card {
    flex-direction: column;
    align-items: flex-start;
    min-width: 320px;
    max-width: 380px;
    flex: 1 1 300px;
    text-align: left;
    padding: var(--space-3xl);
  }
  .feature-icon {
    margin-right: 0;
    margin-bottom: var(--space-lg);
  }
}

/* Launch Section */
.landing-launch {
  background: var(--color-primary-cream);
  color: var(--color-text-primary);
  padding: var(--space-2xl) var(--space-lg);
  position: relative;
}

/* Professional section separator */
.landing-launch::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(89, 28, 40, 0.1);
}

.launch-container {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.launch-container h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 var(--space-2xl) 0;
}

.launch-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-2xl);
  align-items: start;
  margin-bottom: var(--space-2xl);
}

.launch-message {
  text-align: left;
}

.launch-icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
}

.launch-message h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin: 0 0 var(--space-lg) 0;
  color: var(--color-accent-highlight);
}

.launch-message p {
  font-size: var(--text-lg);
  line-height: 1.7;
  opacity: 0.95;
  margin: 0;
}

.launch-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.launch-feature {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  text-align: left;
  background: var(--color-card-bg);
  padding: var(--space-lg);
  border-radius: 1rem; /* Consistent card border radius */
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px var(--color-card-shadow), 0 2px 4px -2px var(--color-card-shadow);
}

.feature-emoji {
  font-size: 2rem;
  flex-shrink: 0;
}

.launch-feature h4 {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-accent-highlight);
}

.launch-feature p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.stats-note {
  background: var(--color-card-bg);
  padding: var(--space-lg);
  border-radius: 1rem; /* Consistent card border radius */
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--color-accent-highlight);
  box-shadow: 0 4px 6px -1px var(--color-card-shadow), 0 2px 4px -2px var(--color-card-shadow);
}

.stats-note p {
  margin: 0;
  font-style: italic;
  opacity: 0.9;
  color: var(--color-text-secondary);
}

/* About Us Section - Enhanced spacing and centering */
.landing-about {
  background: var(--color-primary-cream);
  color: var(--color-text-primary);
  padding: var(--space-4xl) var(--space-2xl);
  position: relative;
  overflow: hidden;
}

/* Professional section separator */
.landing-about::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(110, 140, 101, 0.2);
  pointer-events: none;
  z-index: 1;
}

.about-container {
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.about-header {
  text-align: center;
  margin-bottom: var(--space-4xl);
}

.about-header h2 {
  font-size: var(--text-4xl);
  font-weight: 800;
  color: var(--color-text-primary);
  margin: 0 0 var(--space-lg) 0;
  line-height: 1.2;
}

.about-subtitle {
  font-size: var(--text-xl);
  color: var(--color-text-secondary);
  margin: 0;
  font-weight: 500;
  opacity: 0.9;
  line-height: 1.4;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-4xl);
  margin-bottom: var(--space-4xl);
}

/* Story cards - Enhanced spacing and visual hierarchy */
.founder-story {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-2xl);
}

.story-card {
  background: var(--color-card-bg);
  border-radius: 1.5rem;
  padding: var(--space-3xl);
  box-shadow: 0 8px 32px rgba(89, 28, 40, 0.12);
  transition: all var(--transition-normal);
  border: 2px solid rgba(89, 28, 40, 0.1);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.story-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: var(--color-text-secondary);
}

.story-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 16px 48px rgba(89, 28, 40, 0.18);
  border-color: var(--color-text-primary);
}

.story-card:nth-child(1) {
  animation-delay: 0.1s;
}

.story-card:nth-child(2) {
  animation-delay: 0.2s;
}

.story-card:nth-child(3) {
  animation-delay: 0.3s;
}

.story-icon {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-lg);
  display: block;
}

.story-card h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0 0 var(--space-lg) 0;
  line-height: 1.3;
}

.story-card p {
  font-size: var(--text-lg);
  line-height: 1.7;
  color: var(--color-text-secondary);
  margin: 0;
}

/* Mission statement - Enhanced spacing and centering */
.mission-statement {
  background: var(--color-card-bg);
  border-radius: 1.5rem;
  border: 3px solid var(--color-text-secondary);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(89, 28, 40, 0.12);
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.mission-card {
  padding: var(--space-4xl);
  color: var(--color-text-primary);
  text-align: center;
}

.mission-card h3 {
  font-size: var(--text-3xl);
  font-weight: 800;
  margin: 0 0 var(--space-2xl) 0;
  color: var(--color-text-primary);
  line-height: 1.2;
}

.mission-card p {
  font-size: var(--text-xl);
  line-height: 1.7;
  margin: 0 0 var(--space-3xl) 0;
  color: var(--color-text-secondary);
  opacity: 0.95;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.mission-values {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-lg);
  margin-top: var(--space-3xl);
}

.value-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  background: var(--color-card-bg);
  border-radius: 50px;
  border: 2px solid var(--color-text-secondary);
  transition: all var(--transition-normal);
  box-shadow: 0 4px 16px rgba(110, 140, 101, 0.12);
}

.value-item:hover {
  background: var(--color-card-bg);
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(110, 140, 101, 0.2);
  border-color: var(--color-accent-highlight);
}

.value-icon {
  font-size: var(--text-xl);
  color: var(--color-text-secondary);
}

.about-closing {
  background: var(--color-card-bg);
  color: var(--color-text-primary);
  border: 3px solid var(--color-text-primary);
  padding: var(--space-4xl);
  border-radius: 1.5rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(89, 28, 40, 0.12);
  animation: fadeInUp 1s ease-out 0.6s both;
}

.about-closing h3 {
  font-size: var(--text-3xl);
  font-weight: 800;
  margin: 0 0 var(--space-lg) 0;
  color: var(--color-text-primary);
  line-height: 1.2;
}

.about-closing p {
  font-size: var(--text-xl);
  line-height: 1.7;
  margin: 0;
  color: var(--color-text-secondary);
  opacity: 0.95;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* CTA Section - Enhanced spacing and centering */
.landing-cta {
  background: var(--color-primary-cream);
  color: var(--color-text-primary);
  padding: var(--space-5xl) var(--space-2xl);
  text-align: center;
  position: relative;
}

/* Professional section separator */
.landing-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(89, 28, 40, 0.1);
  pointer-events: none;
  z-index: 1;
}

.cta-container {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.cta-container h2 {
  font-size: var(--text-4xl);
  font-weight: 700;
  margin: 0 0 var(--space-2xl) 0;
  line-height: 1.2;
}

.cta-container p {
  font-size: var(--text-xl);
  line-height: 1.7;
  margin: 0 0 var(--space-4xl) 0;
  opacity: 0.95;
}

.cta-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2xl);
}

/* Enhanced CTA buttons with better spacing */
.cta-primary.large {
  padding: 24px 48px;
  font-size: var(--text-2xl);
  background: transparent;
  color: var(--color-text-secondary);
  box-shadow: 0 12px 36px rgba(110, 140, 101, 0.18);
  font-weight: 800;
  min-height: 70px;
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 1px;
  border: 3px solid var(--color-text-secondary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 250px;
}

.cta-primary.large:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 50px rgba(247, 208, 70, 0.4);
  background: var(--color-button-hover-primary);
  color: var(--color-text-primary);
  border-color: var(--color-accent-highlight);
}

.cta-secondary.large {
  padding: 24px 48px;
  font-size: var(--text-2xl);
  background: transparent;
  color: var(--color-text-secondary);
  border: 3px solid var(--color-text-secondary);
  box-shadow: 0 12px 36px rgba(110, 140, 101, 0.18);
  font-weight: 800;
  min-height: 70px;
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 250px;
}

.cta-secondary.large:hover {
  transform: translateY(-6px);
  background: var(--color-button-hover-secondary);
  color: var(--color-primary-cream);
  border-color: var(--color-text-secondary);
  box-shadow: 0 20px 50px rgba(110, 140, 101, 0.4);
}

.cta-note {
  margin: var(--space-lg) 0 0 0;
  color: var(--color-text-secondary);
  font-size: var(--text-lg);
}

.guest-note {
  margin-top: var(--space-xl);
  padding: var(--space-lg) var(--space-xl);
  background: transparent;
  border-radius: 9999px;
  border-left: 4px solid var(--color-accent-highlight);
  color: var(--color-text-secondary);
  font-size: var(--text-base);
  line-height: 1.6;
  backdrop-filter: blur(10px);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.guest-note strong {
  color: var(--color-accent-highlight);
}

.link-button {
  background: transparent;
  border: 2px solid var(--color-text-secondary);
  color: var(--color-text-secondary);
  text-decoration: none;
  cursor: pointer;
  font-size: inherit;
  font-weight: 600;
  margin-left: var(--space-xs);
  transition: all var(--transition-normal);
  text-shadow: none;
  padding: 8px 16px; /* Fixed padding for perfect small pill shape */
  border-radius: 9999px; /* Perfect pill shape */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 32px; /* Optimal height for small pill shape */
}

.link-button:hover {
  background: var(--color-button-hover-primary);
  color: var(--color-text-primary);
  border-color: var(--color-accent-highlight);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(247, 208, 70, 0.25);
}

/* Footer - Enhanced spacing and centering */
.landing-footer {
  background: var(--color-card-bg);
  color: var(--color-text-primary);
  border-top: 4px solid var(--color-text-secondary);
  padding: var(--space-3xl) var(--space-2xl) var(--space-2xl) var(--space-2xl);
  position: relative;
}

/* Professional section separator */
.landing-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(253, 251, 245, 0.1);
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2xl);
  flex-wrap: wrap;
  gap: var(--space-2xl);
}

.footer-brand h3 {
  margin: 0;
  font-size: var(--text-2xl);
  color: var(--color-text-primary);
  font-weight: 800;
}

.footer-brand p {
  margin: var(--space-sm) 0 0 0;
  opacity: 0.8;
  font-style: italic;
  color: var(--color-text-secondary);
  font-size: var(--text-base);
}

.footer-links {
  display: flex;
  gap: var(--space-2xl);
  flex-wrap: wrap;
}

.footer-links a {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
  font-size: var(--text-base);
  font-weight: 500;
}

.footer-links a:hover {
  color: var(--color-text-primary);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-2xl);
  border-top: 2px solid rgba(89, 28, 40, 0.2);
  flex-wrap: wrap;
  gap: var(--space-lg);
}

.footer-bottom p {
  margin: 0;
  opacity: 0.7;
  font-size: var(--text-base);
  color: var(--color-text-secondary);
}

.version-info {
  background: var(--color-card-bg);
  border: 2px solid var(--color-text-secondary);
  padding: var(--space-sm) var(--space-md);
  border-radius: 9999px;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--color-text-primary);
}

/* === ENHANCEMENTS FOR CONSISTENCY, OUTLINES, AND ACCESSIBILITY === */

/* Stronger outlines for all cards and pill-shaped elements */
.feature-card,
.story-card,
.mission-statement,
.about-closing,
.value-item,
.cta-primary,
.cta-secondary,
.link-button,
.scroll-text,
.guest-note,
.version-info {
  border-width: 2px !important;
  border-style: solid !important;
  border-color: var(--color-text-secondary, #6E8C65) !important;
  box-shadow: 0 2px 12px rgba(89, 28, 40, 0.08), 0 1.5px 6px rgba(110, 140, 101, 0.08);
}

/* Use green for secondary outlines and accents */
.value-item,
.guest-note {
  border-color: var(--color-text-secondary, #6E8C65) !important;
}

/* Green styling for all clickable buttons */
.cta-primary,
.cta-secondary,
.link-button,
.header-signin {
  border-color: var(--color-text-secondary, #6E8C65) !important;
  color: var(--color-text-secondary, #6E8C65) !important;
  background: transparent !important;
}

/* Add subtle green or burgundy left border for feature and story cards */
.feature-card {
  border-left: 5px solid var(--color-text-secondary, #6E8C65) !important;
}
.story-card {
  border-left: 5px solid var(--color-button-border, #591C28) !important;
}

/* Pill-shaped icons and taglines: add clear outlines and background */
.hero-tagline,
.scroll-text {
  border: 2px solid var(--color-button-border, #591C28) !important;
  background: rgba(253, 251, 245, 0.97) !important;
}

/* Feature and story icons: add circle background and outline */
.feature-icon,
.story-icon {
  background: var(--color-accent-highlight, #F7D046);
  border-radius: 50%;
  border: 2.5px solid var(--color-button-border, #591C28);
  padding: 0.5em;
  margin-bottom: var(--space-md);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(110, 140, 101, 0.10);
}

/* Mission values: green outline and consistent background */
.value-item {
  border: 2px solid var(--color-text-secondary, #6E8C65) !important;
  background: var(--color-card-bg) !important;
}
.value-item:hover {
  background: var(--color-card-bg) !important;
  border-color: var(--color-accent-highlight, #F7D046) !important;
}

/* Focus-visible for accessibility */
button:focus-visible,
.link-button:focus-visible,
.scroll-indicator:focus-visible {
  outline: 3px solid var(--color-accent-highlight, #F7D046) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(247, 208, 70, 0.15) !important;
}

/* Ensure text contrast on all backgrounds */
.mission-statement,
.landing-footer {
  color: var(--color-text-primary) !important;
}
.mission-card h3,
.about-closing h3 {
  color: var(--color-text-primary) !important;
}

/* Footer links: consistent color scheme */
.footer-links a:hover {
  color: var(--color-text-primary, #591C28) !important;
}

/* Add a subtle green shadow to CTA secondary on hover */
.cta-secondary.large:hover {
  box-shadow: 0 15px 40px rgba(110, 140, 101, 0.35) !important;
}

/* --- Scroll Indicator: Remove all container borders and box-shadows --- */
.scroll-indicator {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  background: none !important;
}

/* Remove border from .scroll-indicator and its direct children except .scroll-text and .scroll-chevron::after */
.scroll-indicator > *:not(.scroll-text):not(.scroll-chevron) {
  border: none !important;
  box-shadow: none !important;
  background: none !important;
}

/* Remove border from .scroll-chevron itself (the arrow is in ::after) */
.scroll-chevron {
  border: none !important;
  box-shadow: none !important;
  background: none !important;
}

/* Remove any global border rules for .scroll-indicator from the enhancements section */
.feature-card,
.story-card,
.mission-statement,
.about-closing,
.value-item,
.scroll-text,
.guest-note,
.version-info {
  /* .scroll-indicator and buttons removed from this list to prevent border override */
  border-width: 2px !important;
  border-style: solid !important;
  border-color: var(--color-text-secondary, #6E8C65) !important;
  box-shadow: 0 2px 12px rgba(89, 28, 40, 0.08), 0 1.5px 6px rgba(110, 140, 101, 0.08);
}

/* Responsive Design - Enhanced spacing for mobile */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--space-3xl);
    padding: 0 var(--space-lg);
  }

  .hero-text {
    text-align: center;
  }

  .hero-actions {
    justify-content: center;
  }

  /* Override the single button centering for mobile */
  .hero-actions:has(.cta-secondary:only-child) {
    justify-content: center;
  }

  .cta-primary, .cta-secondary {
    width: 100%;
    max-width: 350px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
  }

  .feature-card {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
    padding: var(--space-2xl);
  }

  .feature-icon {
    margin-right: 0;
    margin-bottom: var(--space-lg);
  }

  .launch-content {
    grid-template-columns: 1fr;
    gap: var(--space-3xl);
  }

  .launch-message {
    text-align: center;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2xl);
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: var(--space-lg);
  }

  .footer-links {
    gap: var(--space-lg);
    justify-content: center;
  }

  /* Enhanced mobile spacing */
  .landing-hero {
    padding: calc(var(--space-3xl) + 80px) var(--space-lg) var(--space-3xl);
  }

  .landing-features,
  .landing-about,
  .landing-cta {
    padding: var(--space-3xl) var(--space-lg);
  }

  .about-header {
    margin-bottom: var(--space-3xl);
  }

  .about-content {
    gap: var(--space-3xl);
    margin-bottom: var(--space-3xl);
  }

  .cta-actions {
    gap: var(--space-xl);
  }
}

/* Mobile - Extra small screens */
@media (max-width: 480px) {
  .landing-hero {
    padding: calc(var(--space-2xl) + 80px) var(--space-md) var(--space-2xl);
  }

  .landing-features,
  .landing-about,
  .landing-cta {
    padding: var(--space-2xl) var(--space-md);
  }

  .hero-content {
    gap: var(--space-2xl);
  }

  .brand-name {
    font-size: clamp(2.5rem, 10vw, 4rem);
  }

  .brand-subtitle {
    font-size: clamp(1rem, 4vw, 1.5rem);
  }

  .hero-description {
    font-size: var(--text-base);
    margin-bottom: var(--space-2xl);
  }

  .features-container h2,
  .about-header h2,
  .cta-container h2 {
    font-size: var(--text-2xl);
  }

  .features-container::after {
    margin: var(--space-lg) auto var(--space-2xl) auto;
  }

  .feature-card {
    padding: var(--space-lg);
    gap: var(--space-lg);
  }

  .feature-icon {
    min-width: 50px;
    min-height: 50px;
    font-size: 2rem;
  }

  .story-card {
    padding: var(--space-lg);
  }

  .story-card h3 {
    font-size: var(--text-lg);
  }

  .story-card p {
    font-size: var(--text-base);
  }

  .mission-card {
    padding: var(--space-2xl);
  }

  .mission-card h3 {
    font-size: var(--text-xl);
  }

  .mission-card p {
    font-size: var(--text-base);
  }

  .mission-values {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .value-item {
    padding: var(--space-md);
    font-size: var(--text-sm);
  }

  .about-closing {
    padding: var(--space-2xl);
  }

  .about-closing h3 {
    font-size: var(--text-xl);
  }

  .about-closing p {
    font-size: var(--text-base);
  }

  .cta-container {
    max-width: 100%;
  }

  .cta-container p {
    font-size: var(--text-base);
  }

  .cta-primary.large,
  .cta-secondary.large {
    padding: 18px 36px;
    font-size: var(--text-lg);
    min-height: 60px;
    min-width: 200px;
  }

  .cta-actions {
    gap: var(--space-lg);
  }

  .guest-note {
    padding: var(--space-md);
    font-size: var(--text-sm);
  }

  .launch-features {
    gap: var(--space-md);
  }

  .launch-feature {
    padding: var(--space-md);
  }

  .scroll-indicator {
    bottom: 1.5rem;
  }

  .scroll-text {
    font-size: 0.75rem;
    padding: 0.3em 1.2em;
  }

  .scroll-chevron {
    width: 20px;
    height: 20px;
  }

  .header-content {
    padding: var(--space-sm) var(--space-md);
  }

  .header-logo .logo-image {
    height: 32px;
    width: 32px;
  }

  .header-logo .logo-text {
    font-size: var(--text-lg);
  }

  .header-signin {
    padding: 8px 16px;
    font-size: 0.75rem;
    min-height: 36px;
  }
}

/* Tablet Styles for About Us - Enhanced spacing */
@media (min-width: 768px) {
  .about-content {
    grid-template-columns: 2fr 1fr;
    gap: var(--space-5xl);
    align-items: start;
  }

  .founder-story {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
  }

  .mission-values {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .value-item {
    justify-content: center;
    text-align: center;
    flex-direction: column;
    gap: var(--space-sm);
    padding: var(--space-xl);
  }

  .about-header h2 {
    font-size: var(--text-4xl);
  }

  .about-subtitle {
    font-size: var(--text-xl);
  }

  .features-grid {
    justify-content: center;
  }
}

/* Desktop Styles for About Us - Enhanced spacing */
@media (min-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: var(--space-4xl);
  }

  .founder-story {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-2xl);
  }

  .mission-values {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-lg);
  }

  .value-item {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
    gap: var(--space-md);
    padding: var(--space-lg);
  }

  .story-card {
    padding: var(--space-3xl);
  }

  .mission-card {
    padding: var(--space-4xl);
  }

  .about-header h2 {
    font-size: var(--text-4xl);
  }

  .about-closing {
    padding: var(--space-4xl);
  }
}

/* Mobile Styles for About Us - Enhanced for better spacing */
@media (max-width: 480px) {
  .landing-about {
    padding: var(--space-2xl) var(--space-md);
  }

  .about-header {
    margin-bottom: var(--space-2xl);
  }

  .about-header h2 {
    font-size: var(--text-2xl);
  }

  .about-subtitle {
    font-size: var(--text-base);
  }

  .about-content {
    gap: var(--space-2xl);
    margin-bottom: var(--space-2xl);
  }

  .founder-story {
    gap: var(--space-xl);
  }

  .story-card {
    padding: var(--space-lg);
  }

  .story-card h3 {
    font-size: var(--text-lg);
  }

  .story-card p {
    font-size: var(--text-sm);
  }

  .mission-card {
    padding: var(--space-lg);
  }

  .mission-card h3 {
    font-size: var(--text-xl);
  }

  .mission-card p {
    font-size: var(--text-base);
  }

  .mission-values {
    grid-template-columns: 1fr;
    gap: var(--space-sm);
  }

  .value-item {
    padding: var(--space-sm);
    font-size: var(--text-sm);
  }

  .about-closing {
    padding: var(--space-lg);
  }

  .about-closing h3 {
    font-size: var(--text-xl);
  }

  .about-closing p {
    font-size: var(--text-base);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .landing-page {
    background: #000;
    color: #fff;
  }

  .landing-features {
    background: #fff;
    color: #000;
  }

  .feature-card {
    background: #f0f0f0;
    border: 2px solid #000;
  }

  .cta-primary {
    background: #fff;
    color: #000;
    border: 2px solid #000;
  }

  .cta-secondary {
    background: transparent;
    border: 2px solid #fff;
  }

  .landing-about {
    background: #fff;
    color: #000;
  }

  .story-card {
    background: #f0f0f0;
    border: 2px solid #000;
    color: #000;
  }

  .mission-statement {
    background: #000;
    color: #fff;
  }

  .mission-card {
    color: #fff;
  }

  .value-item {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid #000;
    color: #000;
  }

  .about-closing {
    background: #000;
    color: #fff;
    border: 2px solid #fff;
  }
}

/* === AUTHENTICATION MODALS === */
.auth-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px); /* Safari support */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-lg);
  animation: fadeIn 0.3s ease-out;
}

.auth-modal-content {
  background: var(--color-card-bg);
  border-radius: 1rem;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.3),
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  border: 2px solid var(--color-text-secondary);
  position: relative;
  max-width: 500px;
  width: 100%;
  overflow: visible;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.auth-modal-close {
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  background: var(--color-card-bg);
  border: 2px solid var(--color-text-secondary);
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: var(--text-lg);
  color: var(--color-text-secondary);
  transition: all var(--transition-normal);
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.auth-modal-close:hover {
  background: var(--color-button-hover-primary);
  color: var(--color-text-primary);
  border-color: var(--color-accent-highlight);
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 4px 15px rgba(247, 208, 70, 0.25);
}

.auth-modal-close:focus-visible {
  outline: 3px solid var(--color-accent-highlight);
  outline-offset: 2px;
}

/* Remove the back button from auth forms when in modal */
.auth-modal-overlay .auth-modal-content .back-to-landing,
.auth-modal-content .auth-form .back-to-landing {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  position: absolute !important;
  left: -9999px !important;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* Override auth form styling in modal */
.auth-modal-content .auth-form {
  background: var(--color-card-bg) !important;
  backdrop-filter: none !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 1rem !important;
  margin: 0 !important;
  padding: var(--space-2xl) !important;
  padding-top: calc(var(--space-2xl) + 20px) !important; /* Account for close button */
  max-width: none !important;
}

/* Ensure auth brand section looks good in modal */
.auth-modal-content .auth-brand {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.auth-modal-content .auth-logo {
  width: 60px !important;
  height: 60px !important;
  margin-bottom: var(--space-md) !important;
  filter: none !important;
  animation: none !important;
}

.auth-modal-content .auth-brand h2 {
  color: var(--color-text-primary) !important;
  font-size: var(--text-2xl) !important;
  font-weight: 700 !important;
  margin-bottom: var(--space-sm) !important;
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: unset !important;
  background-clip: unset !important;
}

.auth-modal-content .auth-subtitle {
  color: var(--color-text-secondary) !important;
  font-size: var(--text-base) !important;
  margin-bottom: 0 !important;
}

/* Style form inputs to match NAROOP design */
.auth-modal-content input {
  width: 100% !important;
  padding: var(--space-md) !important;
  border: 2px solid var(--color-text-secondary) !important;
  border-radius: 0.5rem !important;
  font-size: var(--text-base) !important;
  background: var(--color-card-bg) !important;
  color: var(--color-text-primary) !important;
  margin-bottom: var(--space-md) !important;
  transition: all var(--transition-normal) !important;
  backdrop-filter: none !important;
  box-shadow: none !important;
  transform: none !important;
  font-weight: 400 !important;
}

.auth-modal-content input:focus {
  outline: none !important;
  border-color: var(--color-accent-highlight) !important;
  box-shadow: 0 0 0 3px rgba(247, 208, 70, 0.2) !important;
  background: var(--color-card-bg) !important;
  transform: none !important;
}

.auth-modal-content input::placeholder {
  color: var(--color-text-secondary) !important;
  opacity: 0.7 !important;
  font-weight: 400 !important;
}

/* Style form buttons to match NAROOP design */
.auth-modal-content button[type="submit"] {
  width: 100% !important;
  background: transparent !important;
  border: 2px solid var(--color-text-secondary) !important;
  color: var(--color-text-secondary) !important;
  padding: var(--space-md) var(--space-xl) !important;
  border-radius: 9999px !important;
  font-size: var(--text-base) !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all var(--transition-normal) !important;
  margin: var(--space-lg) 0 !important;
  min-height: 48px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  box-shadow: none !important;
  overflow: visible !important;
  position: relative !important;
}

.auth-modal-content button[type="submit"]:before {
  display: none !important;
}

.auth-modal-content button[type="submit"]:hover:not(:disabled) {
  background: var(--color-button-hover-primary) !important;
  color: var(--color-text-primary) !important;
  border-color: var(--color-accent-highlight) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(247, 208, 70, 0.35) !important;
}

.auth-modal-content button[type="submit"]:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Style auth links */
.auth-modal-content .auth-links {
  text-align: center !important;
  margin-top: var(--space-lg) !important;
}

.auth-modal-content .auth-links p {
  color: var(--color-text-secondary) !important;
  margin-bottom: var(--space-sm) !important;
  font-size: var(--text-base) !important;
  font-weight: 400 !important;
}

.auth-modal-content .auth-links button {
  background: transparent !important;
  border: none !important;
  color: var(--color-text-primary) !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  text-decoration: underline !important;
  transition: color var(--transition-normal) !important;
  font-size: inherit !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
  min-height: auto !important;
  display: inline !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  box-shadow: none !important;
}

.auth-modal-content .auth-links button:hover {
  color: var(--color-accent-highlight) !important;
  background: transparent !important;
  border: none !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Error messages */
.auth-modal-content .auth-error {
  background: rgba(220, 53, 69, 0.1) !important;
  border: 1px solid rgba(220, 53, 69, 0.3) !important;
  color: #dc3545 !important;
  padding: var(--space-md) !important;
  border-radius: 0.5rem !important;
  margin-bottom: var(--space-lg) !important;
  font-size: var(--text-sm) !important;
  text-align: center !important;
  backdrop-filter: none !important;
  box-shadow: none !important;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    transform: translateY(-30px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .auth-modal-overlay {
    padding: var(--space-md);
  }
  
  .auth-modal-content {
    max-width: 100%;
    margin: 0 auto;
  }
  
  .auth-modal-content .auth-form {
    padding: var(--space-lg) !important;
    padding-top: calc(var(--space-lg) + 30px) !important;
  }
  
  .auth-modal-close {
    top: var(--space-md);
    right: var(--space-md);
    width: 40px;
    height: 40px;
    font-size: var(--text-base);
  }
}

@media (max-width: 480px) {
  .auth-modal-overlay {
    padding: var(--space-sm);
  }
  
  .auth-modal-content {
    border-radius: 0.75rem;
  }
  
  .auth-modal-content .auth-form {
    padding: var(--space-md) !important;
    padding-top: calc(var(--space-md) + 30px) !important;
  }
  
  .auth-modal-close {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }
  
  .auth-modal-content .auth-brand h2 {
    font-size: var(--text-xl) !important;
  }
  
  .auth-modal-content .auth-logo {
    width: 50px !important;
    height: 50px !important;
  }
}
