import React, { useState, useEffect } from 'react';
import { useAuth } from '../AuthContext';
import {
  getUserRole,
  hasPermission,
  getAdminUsers,
  getAdminLogs,
  setUserRole,
  removeAdminRole,
  ADMIN_ROLES
} from '../services/admin';
import {
  getPendingContent,
  getReportedContent,
  approveContent,
  rejectContent,
  removeContent
} from '../services/moderation';
import {
  warnUser,
  suspendUser,
  banUser,
  getUserModerationHistory,
  WARNING_TYPES
} from '../services/userManagement';
import './AdminDashboard.css';

/**
 * Admin Dashboard Component
 * Comprehensive admin interface for platform management
 */
export default function AdminDashboard({ onClose }) {
  const { currentUser } = useAuth();
  const [userRole, setUserRole] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [dashboardData, setDashboardData] = useState({
    stats: {},
    pendingContent: [],
    reportedContent: [],
    adminUsers: [],
    recentLogs: []
  });
  const [actionLoading, setActionLoading] = useState({});

  useEffect(() => {
    if (!currentUser) return;

    const loadAdminData = async () => {
      try {
        // Check user role and permissions
        const role = await getUserRole(currentUser.uid);
        setUserRole(role);

        // Only load data if user has admin permissions
        if (role.role === ADMIN_ROLES.USER) {
          setLoading(false);
          return;
        }

        // Load dashboard data
        const [pendingContent, reportedContent, adminUsers, recentLogs] = await Promise.all([
          getPendingContent(10),
          getReportedContent(10),
          getAdminUsers(),
          getAdminLogs({}, 20)
        ]);

        // Calculate basic stats
        const stats = {
          pendingContentCount: pendingContent.length,
          reportedContentCount: reportedContent.length,
          adminUserCount: adminUsers.length,
          totalActions: recentLogs.length
        };

        setDashboardData({
          stats,
          pendingContent,
          reportedContent,
          adminUsers,
          recentLogs
        });
      } catch (error) {
        console.error('Error loading admin data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAdminData();
  }, [currentUser]);

  // Moderation action handlers
  const handleApproveContent = async (contentId) => {
    setActionLoading(prev => ({ ...prev, [contentId]: true }));
    try {
      await approveContent(contentId, currentUser.uid, 'Approved by admin');
      // Refresh pending content
      const updatedPending = await getPendingContent(10);
      setDashboardData(prev => ({ ...prev, pendingContent: updatedPending }));
      alert('Content approved successfully!');
    } catch (error) {
      console.error('Error approving content:', error);
      alert('Failed to approve content: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [contentId]: false }));
    }
  };

  const handleRejectContent = async (contentId) => {
    const reason = prompt('Please provide a reason for rejection:');
    if (!reason) return;

    setActionLoading(prev => ({ ...prev, [contentId]: true }));
    try {
      await rejectContent(contentId, currentUser.uid, reason);
      // Refresh pending content
      const updatedPending = await getPendingContent(10);
      setDashboardData(prev => ({ ...prev, pendingContent: updatedPending }));
      alert('Content rejected successfully!');
    } catch (error) {
      console.error('Error rejecting content:', error);
      alert('Failed to reject content: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [contentId]: false }));
    }
  };

  const handleRemoveContent = async (contentId) => {
    const reason = prompt('Please provide a reason for removal:');
    if (!reason) return;

    if (!confirm('Are you sure you want to remove this content? This action cannot be undone.')) {
      return;
    }

    setActionLoading(prev => ({ ...prev, [contentId]: true }));
    try {
      await removeContent(contentId, currentUser.uid, reason);
      // Refresh reported content
      const updatedReported = await getReportedContent(10);
      setDashboardData(prev => ({ ...prev, reportedContent: updatedReported }));
      alert('Content removed successfully!');
    } catch (error) {
      console.error('Error removing content:', error);
      alert('Failed to remove content: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [contentId]: false }));
    }
  };

  // User management handlers
  const handleWarnUser = async (userId, reason = '') => {
    const warningReason = reason || prompt('Please provide a reason for the warning:');
    if (!warningReason) return;

    const warningType = prompt(`Select warning type:
1. Content Violation
2. Community Guidelines
3. Harassment
4. Spam
5. Inappropriate Behavior
6. Final Warning

Enter number (1-6):`);

    const warningTypes = {
      '1': WARNING_TYPES.CONTENT_VIOLATION,
      '2': WARNING_TYPES.COMMUNITY_GUIDELINES,
      '3': WARNING_TYPES.HARASSMENT,
      '4': WARNING_TYPES.SPAM,
      '5': WARNING_TYPES.INAPPROPRIATE_BEHAVIOR,
      '6': WARNING_TYPES.FINAL_WARNING
    };

    const selectedType = warningTypes[warningType] || WARNING_TYPES.COMMUNITY_GUIDELINES;

    setActionLoading(prev => ({ ...prev, [userId]: true }));
    try {
      await warnUser(userId, currentUser.uid, selectedType, warningReason);
      alert('User warned successfully!');
    } catch (error) {
      console.error('Error warning user:', error);
      alert('Failed to warn user: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [userId]: false }));
    }
  };

  const handleSuspendUser = async (userId) => {
    const reason = prompt('Please provide a reason for suspension:');
    if (!reason) return;

    const durationStr = prompt('Suspension duration in days (1-30):');
    const duration = parseInt(durationStr);

    if (!duration || duration < 1 || duration > 30) {
      alert('Please enter a valid duration between 1 and 30 days.');
      return;
    }

    if (!confirm(`Are you sure you want to suspend this user for ${duration} days?`)) {
      return;
    }

    setActionLoading(prev => ({ ...prev, [userId]: true }));
    try {
      await suspendUser(userId, currentUser.uid, duration, reason);
      alert(`User suspended for ${duration} days successfully!`);
    } catch (error) {
      console.error('Error suspending user:', error);
      alert('Failed to suspend user: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [userId]: false }));
    }
  };

  const handleBanUser = async (userId) => {
    const reason = prompt('Please provide a reason for permanent ban:');
    if (!reason) return;

    if (!confirm('Are you sure you want to PERMANENTLY BAN this user? This action cannot be undone.')) {
      return;
    }

    setActionLoading(prev => ({ ...prev, [userId]: true }));
    try {
      await banUser(userId, currentUser.uid, reason);
      alert('User banned permanently!');
    } catch (error) {
      console.error('Error banning user:', error);
      alert('Failed to ban user: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [userId]: false }));
    }
  };

  // Moderator management handlers
  const handleAssignModeratorRole = async (userId) => {
    const roleOptions = `Select role to assign:
1. Community Helper (Limited moderation)
2. Moderator (Content moderation)
3. Admin (Standard admin access)
4. Super Admin (Nearly full access)

Enter number (1-4):`;

    const roleChoice = prompt(roleOptions);

    const roleMap = {
      '1': ADMIN_ROLES.COMMUNITY_HELPER,
      '2': ADMIN_ROLES.MODERATOR,
      '3': ADMIN_ROLES.ADMIN,
      '4': ADMIN_ROLES.SUPER_ADMIN
    };

    const selectedRole = roleMap[roleChoice];
    if (!selectedRole) {
      alert('Invalid role selection.');
      return;
    }

    // Only platform owner can assign super admin
    if (selectedRole === ADMIN_ROLES.SUPER_ADMIN && userRole?.role !== ADMIN_ROLES.PLATFORM_OWNER) {
      alert('Only the platform owner can assign Super Admin roles.');
      return;
    }

    if (!confirm(`Are you sure you want to assign ${selectedRole.replace('_', ' ')} role to this user?`)) {
      return;
    }

    setActionLoading(prev => ({ ...prev, [userId]: true }));
    try {
      await setUserRole(userId, selectedRole, currentUser.uid);
      // Refresh admin users list
      const updatedAdmins = await getAdminUsers();
      setDashboardData(prev => ({ ...prev, adminUsers: updatedAdmins }));
      alert('Role assigned successfully!');
    } catch (error) {
      console.error('Error assigning role:', error);
      alert('Failed to assign role: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [userId]: false }));
    }
  };

  const handleRemoveModeratorRole = async (userId) => {
    if (!confirm('Are you sure you want to remove this user\'s admin/moderator role?')) {
      return;
    }

    setActionLoading(prev => ({ ...prev, [userId]: true }));
    try {
      await removeAdminRole(userId, currentUser.uid);
      // Refresh admin users list
      const updatedAdmins = await getAdminUsers();
      setDashboardData(prev => ({ ...prev, adminUsers: updatedAdmins }));
      alert('Role removed successfully!');
    } catch (error) {
      console.error('Error removing role:', error);
      alert('Failed to remove role: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [userId]: false }));
    }
  };

  const renderOverview = () => (
    <div className="admin-overview">
      <div className="admin-stats-grid">
        <div className="admin-stat-card">
          <div className="stat-icon">📝</div>
          <div className="stat-content">
            <h3>{dashboardData.stats.pendingContentCount || 0}</h3>
            <p>Pending Content</p>
          </div>
        </div>
        
        <div className="admin-stat-card">
          <div className="stat-icon">🚨</div>
          <div className="stat-content">
            <h3>{dashboardData.stats.reportedContentCount || 0}</h3>
            <p>Reported Content</p>
          </div>
        </div>
        
        <div className="admin-stat-card">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <h3>{dashboardData.stats.adminUserCount || 0}</h3>
            <p>Admin Users</p>
          </div>
        </div>
        
        <div className="admin-stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-content">
            <h3>{dashboardData.stats.totalActions || 0}</h3>
            <p>Recent Actions</p>
          </div>
        </div>
      </div>

      <div className="admin-quick-actions">
        <h3>Quick Actions</h3>
        <div className="quick-actions-grid">
          <button 
            className="quick-action-btn"
            onClick={() => setActiveTab('content')}
          >
            📝 Review Content
          </button>
          <button 
            className="quick-action-btn"
            onClick={() => setActiveTab('reports')}
          >
            🚨 Handle Reports
          </button>
          <button 
            className="quick-action-btn"
            onClick={() => setActiveTab('users')}
          >
            👤 Manage Users
          </button>
          <button 
            className="quick-action-btn"
            onClick={() => setActiveTab('logs')}
          >
            📋 View Logs
          </button>
        </div>
      </div>

      <div className="admin-recent-activity">
        <h3>Recent Admin Activity</h3>
        <div className="activity-list">
          {dashboardData.recentLogs.slice(0, 5).map(log => (
            <div key={log.id} className="activity-item">
              <div className="activity-icon">
                {getActionIcon(log.action)}
              </div>
              <div className="activity-content">
                <p className="activity-action">{formatActionText(log.action)}</p>
                <p className="activity-details">
                  by {log.performedBy} • {formatTimestamp(log.timestamp)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderContentModeration = () => (
    <div className="admin-content-moderation">
      <h3>Content Moderation</h3>
      
      <div className="moderation-section">
        <h4>Pending Content ({dashboardData.pendingContent.length})</h4>
        <div className="content-list">
          {dashboardData.pendingContent.map(item => (
            <div key={item.contentId} className="content-item">
              <div className="content-header">
                <h5>{item.contentData?.title || 'Untitled'}</h5>
                <span className={`status-badge ${item.status}`}>
                  {item.status}
                </span>
              </div>
              <p className="content-preview">
                {item.contentData?.content?.substring(0, 150)}...
              </p>
              <div className="content-meta">
                <span>By: {item.contentData?.author}</span>
                <span>Submitted: {formatTimestamp(item.submittedAt)}</span>
              </div>
              <div className="content-actions">
                <button
                  className="approve-btn"
                  onClick={() => handleApproveContent(item.contentId)}
                  disabled={actionLoading[item.contentId]}
                >
                  {actionLoading[item.contentId] ? '⏳' : '✅'} Approve
                </button>
                <button
                  className="reject-btn"
                  onClick={() => handleRejectContent(item.contentId)}
                  disabled={actionLoading[item.contentId]}
                >
                  {actionLoading[item.contentId] ? '⏳' : '❌'} Reject
                </button>
                <button className="view-btn">👁️ View Full</button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderReports = () => (
    <div className="admin-reports">
      <h3>Reported Content</h3>
      
      <div className="reports-list">
        {dashboardData.reportedContent.map(report => (
          <div key={report.id} className="report-item">
            <div className="report-header">
              <h5>{report.contentData?.title || 'Untitled'}</h5>
              <span className="report-reason">{report.reason}</span>
            </div>
            <p className="report-details">{report.details}</p>
            <div className="report-meta">
              <span>Reported by: {report.reporterId}</span>
              <span>Date: {formatTimestamp(report.reportedAt)}</span>
            </div>
            <div className="report-actions">
              <button className="dismiss-btn">Dismiss</button>
              <button
                className="remove-btn"
                onClick={() => handleRemoveContent(report.contentId)}
                disabled={actionLoading[report.contentId]}
              >
                {actionLoading[report.contentId] ? '⏳' : '🗑️'} Remove Content
              </button>
              <button
                className="warn-btn"
                onClick={() => handleWarnUser(report.contentData?.author)}
                disabled={actionLoading[report.contentData?.author]}
              >
                {actionLoading[report.contentData?.author] ? '⏳' : '⚠️'} Warn User
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAdminUsers = () => (
    <div className="admin-users">
      <h3>Admin Users</h3>
      
      <div className="admin-users-list">
        {dashboardData.adminUsers.map(admin => (
          <div key={admin.userId} className="admin-user-item">
            <div className="admin-user-info">
              <div className="admin-avatar">
                {admin.userInfo?.avatar || '👤'}
              </div>
              <div className="admin-details">
                <h5>{admin.userInfo?.name || 'Admin User'}</h5>
                <p className="admin-email">{admin.userInfo?.email}</p>
                <span className={`role-badge ${admin.role}`}>
                  {formatRoleName(admin.role)}
                </span>
              </div>
            </div>
            <div className="admin-user-meta">
              <p>Assigned: {formatTimestamp(admin.assignedAt)}</p>
              {admin.assignedBy && <p>By: {admin.assignedBy}</p>}
            </div>
            {(userRole?.role === ADMIN_ROLES.PLATFORM_OWNER || userRole?.role === ADMIN_ROLES.SUPER_ADMIN) && (
              <div className="admin-user-actions">
                <button
                  className="modify-role-btn"
                  onClick={() => handleAssignModeratorRole(admin.userId)}
                  disabled={actionLoading[admin.userId]}
                >
                  {actionLoading[admin.userId] ? '⏳' : '✏️'} Modify Role
                </button>
                {admin.role !== ADMIN_ROLES.PLATFORM_OWNER && (
                  <button
                    className="remove-role-btn"
                    onClick={() => handleRemoveModeratorRole(admin.userId)}
                    disabled={actionLoading[admin.userId]}
                  >
                    {actionLoading[admin.userId] ? '⏳' : '🗑️'} Remove
                  </button>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  const renderUserManagement = () => (
    <div className="admin-user-management">
      <h3>User Management & Safety</h3>

      <div className="user-search-section">
        <h4>Search Users</h4>
        <div className="user-search-form">
          <input
            type="text"
            placeholder="Enter user email or ID"
            className="user-search-input"
          />
          <button className="search-user-btn">🔍 Search</button>
        </div>
      </div>

      {(userRole?.role === ADMIN_ROLES.PLATFORM_OWNER || userRole?.role === ADMIN_ROLES.SUPER_ADMIN) && (
        <div className="moderator-assignment-section">
          <h4>👑 Assign Moderator Roles</h4>
          <p>Search for users above and use the admin users section to assign moderator roles to trusted community members.</p>
          <div className="role-descriptions">
            <div className="role-desc">
              <strong>Community Helper:</strong> Can approve content and issue warnings
            </div>
            <div className="role-desc">
              <strong>Moderator:</strong> Can moderate content, warn, and suspend users
            </div>
            <div className="role-desc">
              <strong>Admin:</strong> Full moderation access plus user management
            </div>
            <div className="role-desc">
              <strong>Super Admin:</strong> Nearly full platform access (Platform Owner only)
            </div>
          </div>
        </div>
      )}

      <div className="user-actions-guide">
        <h4>Available Actions</h4>
        <div className="actions-grid">
          <div className="action-card">
            <div className="action-icon">⚠️</div>
            <h5>Warn User</h5>
            <p>Send a warning for policy violations</p>
          </div>
          <div className="action-card">
            <div className="action-icon">🚫</div>
            <h5>Suspend User</h5>
            <p>Temporarily suspend account (1-30 days)</p>
          </div>
          <div className="action-card">
            <div className="action-icon">🔨</div>
            <h5>Ban User</h5>
            <p>Permanently ban user account</p>
          </div>
          <div className="action-card">
            <div className="action-icon">📋</div>
            <h5>View History</h5>
            <p>Check user's moderation history</p>
          </div>
        </div>
      </div>

      <div className="quick-user-actions">
        <h4>Quick Actions</h4>
        <p>Use the report handling section above to take action on specific users based on reported content.</p>
        <div className="user-management-tips">
          <h5>💡 Moderation Tips:</h5>
          <ul>
            <li>Always provide clear reasons for moderation actions</li>
            <li>Start with warnings before escalating to suspensions</li>
            <li>Check user's history before taking severe actions</li>
            <li>Document all actions for accountability</li>
          </ul>
        </div>
      </div>
    </div>
  );

  const renderLogs = () => (
    <div className="admin-logs">
      <h3>Admin Action Logs</h3>
      
      <div className="logs-list">
        {dashboardData.recentLogs.map(log => (
          <div key={log.id} className="log-item">
            <div className="log-icon">
              {getActionIcon(log.action)}
            </div>
            <div className="log-content">
              <h5>{formatActionText(log.action)}</h5>
              <p className="log-details">
                Performed by: {log.performedBy}
              </p>
              <p className="log-timestamp">
                {formatTimestamp(log.timestamp)}
              </p>
              {log.details && (
                <div className="log-extra-details">
                  {JSON.stringify(log.details, null, 2)}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Helper functions
  const getActionIcon = (action) => {
    const icons = {
      'CONTENT_APPROVED': '✅',
      'CONTENT_REJECTED': '❌',
      'CONTENT_REMOVED': '🗑️',
      'USER_WARNED': '⚠️',
      'USER_SUSPENDED': '🚫',
      'USER_BANNED': '🔨',
      'ROLE_ASSIGNED': '👑',
      'ROLE_REMOVED': '👤'
    };
    return icons[action] || '📝';
  };

  const formatActionText = (action) => {
    return action.replace(/_/g, ' ').toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatRoleName = (role) => {
    return role.replace(/_/g, ' ').toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'Unknown';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  if (loading) {
    return (
      <div className="admin-dashboard-modal">
        <div className="admin-dashboard-content">
          <div className="loading-state">Loading admin dashboard...</div>
        </div>
      </div>
    );
  }

  if (!userRole || userRole.role === ADMIN_ROLES.USER) {
    return (
      <div className="admin-dashboard-modal">
        <div className="admin-dashboard-content">
          <div className="access-denied">
            <h2>🚫 Access Denied</h2>
            <p>You don't have permission to access the admin dashboard.</p>
            <button onClick={onClose} className="close-btn">Close</button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-dashboard-modal" onClick={(e) => e.target === e.currentTarget && onClose()}>
      <div className="admin-dashboard-content">
        <div className="admin-header">
          <h2>🛡️ NAROOP Admin Dashboard</h2>
          <div className="admin-user-info">
            <span className={`role-badge ${userRole.role}`}>
              {formatRoleName(userRole.role)}
            </span>
            <button className="close-admin-btn" onClick={onClose}>✕</button>
          </div>
        </div>

        <div className="admin-tabs">
          <button 
            className={`admin-tab ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            📊 Overview
          </button>
          <button 
            className={`admin-tab ${activeTab === 'content' ? 'active' : ''}`}
            onClick={() => setActiveTab('content')}
          >
            📝 Content
          </button>
          <button 
            className={`admin-tab ${activeTab === 'reports' ? 'active' : ''}`}
            onClick={() => setActiveTab('reports')}
          >
            🚨 Reports
          </button>
          <button
            className={`admin-tab ${activeTab === 'users' ? 'active' : ''}`}
            onClick={() => setActiveTab('users')}
          >
            👥 Admins
          </button>
          <button
            className={`admin-tab ${activeTab === 'userManagement' ? 'active' : ''}`}
            onClick={() => setActiveTab('userManagement')}
          >
            🔨 User Mgmt
          </button>
          <button 
            className={`admin-tab ${activeTab === 'logs' ? 'active' : ''}`}
            onClick={() => setActiveTab('logs')}
          >
            📋 Logs
          </button>
        </div>

        <div className="admin-body">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'content' && renderContentModeration()}
          {activeTab === 'reports' && renderReports()}
          {activeTab === 'users' && renderAdminUsers()}
          {activeTab === 'userManagement' && renderUserManagement()}
          {activeTab === 'logs' && renderLogs()}
        </div>
      </div>
    </div>
  );
}
