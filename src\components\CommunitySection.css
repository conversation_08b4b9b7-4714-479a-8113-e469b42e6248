/* Community Section Styles - Consistent with NAROOP Landing Page */
.community-section {
  background: var(--color-primary-cream);
  color: var(--color-text-primary);
  padding: var(--space-2xl) var(--space-lg);
  position: relative;
}

/* Professional section separator */
.community-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(89, 28, 40, 0.1);
}

.community-container {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

.community-container h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 var(--space-2xl) 0;
  color: var(--color-text-primary);
}

/* Launch Mode Styles */
.launch-mode .launch-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-2xl);
  align-items: start;
  margin-bottom: var(--space-2xl);
}

.launch-message {
  text-align: left;
}

.launch-icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
}

.launch-message h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin: 0 0 var(--space-lg) 0;
  color: var(--color-text-primary);
}

.launch-message p {
  font-size: var(--text-lg);
  line-height: 1.7;
  color: var(--color-text-secondary);
  margin: 0;
}

.launch-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.launch-feature {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  text-align: left;
  background: var(--color-card-bg);
  padding: var(--space-lg);
  border-radius: 1rem;
  border: 2px solid var(--color-text-secondary);
  box-shadow: 0 4px 16px rgba(110, 140, 101, 0.10);
  transition: all var(--transition-normal);
}

.launch-feature:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(110, 140, 101, 0.15);
  border-color: var(--color-accent-highlight);
}

.feature-emoji {
  font-size: 2rem;
  flex-shrink: 0;
  background: var(--color-accent-highlight);
  border-radius: 50%;
  border: 2px solid var(--color-button-border);
  padding: 0.5em;
  min-width: 48px;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.launch-feature h4 {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-text-primary);
}

.launch-feature p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
  color: var(--color-text-secondary);
}

.stats-note {
  background: var(--color-card-bg);
  padding: var(--space-lg);
  border-radius: 25px; /* Pill-shaped for consistency */
  border: 2px solid var(--color-heritage-gold);
  box-shadow: 0 4px 16px rgba(110, 140, 101, 0.10);
  transition: all var(--transition-normal);
}

.stats-note p {
  margin: 0;
  font-style: italic;
  opacity: 0.9;
  color: var(--color-text-secondary);
}

/* Stats Mode Styles */
.stats-mode .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-xl);
  margin-bottom: var(--space-2xl);
}

.stat-item {
  text-align: center;
  background: var(--color-card-bg);
  padding: var(--space-xl);
  border-radius: 25px; /* Pill-shaped for consistency */
  transition: all var(--transition-normal);
  border: 2px solid var(--color-text-secondary);
  box-shadow: 0 4px 16px rgba(110, 140, 101, 0.10);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(110, 140, 101, 0.15);
  border-color: var(--color-accent-highlight);
}

.stat-number {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 900;
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
  display: block;
}

.stat-label {
  font-size: var(--text-lg);
  opacity: 0.9;
  font-weight: 500;
  color: var(--color-text-secondary);
}

/* Community Highlights (for future use) */
.community-highlights {
  margin-top: var(--space-2xl);
}

.community-highlights h3 {
  font-size: var(--text-2xl);
  font-weight: 600;
  margin: 0 0 var(--space-xl) 0;
  color: var(--color-text-primary);
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
}

.highlight-item {
  background: var(--color-card-bg);
  padding: var(--space-xl);
  border-radius: 25px; /* Pill-shaped for consistency */
  text-align: center;
  transition: all var(--transition-normal);
  border: 2px solid var(--color-text-secondary);
  box-shadow: 0 4px 16px rgba(110, 140, 101, 0.10);
}

.highlight-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(110, 140, 101, 0.15);
  border-color: var(--color-accent-highlight);
}

.highlight-icon {
  font-size: 3rem;
  margin-bottom: var(--space-md);
}

.highlight-item h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin: 0 0 var(--space-sm) 0;
  color: var(--color-text-primary);
}

.highlight-item p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.6;
  color: var(--color-text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .launch-mode .launch-content {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .launch-message {
    text-align: center;
  }

  .stats-mode .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .highlights-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .community-section {
    padding: var(--space-xl) var(--space-md);
  }

  .launch-features {
    gap: var(--space-md);
  }

  .launch-feature {
    padding: var(--space-md);
  }

  .stats-mode .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-item {
    padding: var(--space-lg);
  }
}

/* Dark mode support */
.dark-mode .community-section {
  background: var(--color-primary-dark);
}

.dark-mode .launch-feature,
.dark-mode .stat-item,
.dark-mode .highlight-item,
.dark-mode .stats-note,
.dark-mode .community-online-section,
.dark-mode .community-online-users,
.dark-mode .community-online-users .online-user-item {
  background: var(--color-card-bg-dark);
  border-color: var(--color-text-secondary-dark);
}

.dark-mode .launch-feature:hover,
.dark-mode .stat-item:hover,
.dark-mode .highlight-item:hover,
.dark-mode .community-online-users .online-user-item:hover {
  border-color: var(--color-accent-highlight);
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .community-section {
    background: var(--color-primary-cream);
    color: var(--color-text-primary);
  }

  .launch-feature,
  .stat-item,
  .highlight-item,
  .stats-note,
  .community-online-section,
  .community-online-users,
  .community-online-users .online-user-item {
    background: var(--color-card-bg);
    border: 3px solid var(--color-text-primary);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  }
}

/* Online Community Section Styles */
.community-online-section {
  margin-top: var(--space-2xl);
  padding: var(--space-xl);
  background: var(--color-card-bg);
  border-radius: 25px; /* Pill-shaped for consistency */
  border: 2px solid var(--color-text-secondary);
  box-shadow: 0 4px 16px rgba(110, 140, 101, 0.10);
}

.online-header {
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.community-online-counter {
  background: var(--color-card-bg);
  color: var(--color-text-primary);
  border: 2px solid var(--color-accent-highlight);
  padding: var(--space-sm) var(--space-lg);
  border-radius: 50px; /* Perfect pill shape */
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(247, 208, 70, 0.25);
}

.community-online-users {
  background: var(--color-card-bg);
  border-radius: 20px; /* Pill-shaped for consistency */
  padding: var(--space-lg);
  border: 2px solid var(--color-text-secondary);
  box-shadow: 0 4px 16px rgba(110, 140, 101, 0.10);
}

.community-online-users .users-header h4 {
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  font-size: var(--text-lg);
}

.community-online-users .online-user-item {
  background: var(--color-card-bg);
  border: 2px solid var(--color-text-secondary);
  transition: all var(--transition-normal);
  box-shadow: 0 2px 8px rgba(110, 140, 101, 0.08);
}

.community-online-users .online-user-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(110, 140, 101, 0.15);
  border-color: var(--color-accent-highlight);
}

.community-online-users .user-name {
  color: var(--color-text-primary);
  font-weight: 500;
}

.community-online-users .presence-text {
  color: var(--color-accent-emerald);
  font-weight: 500;
}

/* Mobile responsive adjustments for online section */
@media (max-width: 768px) {
  .community-online-section {
    padding: var(--space-md);
    margin-top: var(--space-lg);
  }

  .community-online-counter {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--text-sm);
  }

  .community-online-users {
    padding: var(--space-md);
  }
}
