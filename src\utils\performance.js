// Performance monitoring utilities for NAROOP
// Helps track Core Web Vitals and optimize user experience

/**
 * Performance monitoring class for tracking Core Web Vitals
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = {};
    this.observers = [];
    this.init();
  }

  init() {
    if (typeof window === 'undefined') return;

    // Track First Contentful Paint (FCP)
    this.trackFCP();
    
    // Track Largest Contentful Paint (LCP)
    this.trackLCP();
    
    // Track First Input Delay (FID)
    this.trackFID();
    
    // Track Cumulative Layout Shift (CLS)
    this.trackCLS();
    
    // Track Time to Interactive (TTI)
    this.trackTTI();
  }

  trackFCP() {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
        if (fcpEntry) {
          this.metrics.fcp = fcpEntry.startTime;
          this.logMetric('FCP', fcpEntry.startTime);
        }
      });
      observer.observe({ entryTypes: ['paint'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('FCP tracking not supported:', error);
    }
  }

  trackLCP() {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.lcp = lastEntry.startTime;
        this.logMetric('LCP', lastEntry.startTime);
      });
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('LCP tracking not supported:', error);
    }
  }

  trackFID() {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          this.metrics.fid = entry.processingStart - entry.startTime;
          this.logMetric('FID', this.metrics.fid);
        });
      });
      observer.observe({ entryTypes: ['first-input'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('FID tracking not supported:', error);
    }
  }

  trackCLS() {
    try {
      let clsValue = 0;
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.metrics.cls = clsValue;
        this.logMetric('CLS', clsValue);
      });
      observer.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('CLS tracking not supported:', error);
    }
  }

  trackTTI() {
    // Simplified TTI tracking
    if (document.readyState === 'complete') {
      this.calculateTTI();
    } else {
      window.addEventListener('load', () => {
        setTimeout(() => this.calculateTTI(), 100);
      });
    }
  }

  calculateTTI() {
    const navigationEntry = performance.getEntriesByType('navigation')[0];
    if (navigationEntry) {
      this.metrics.tti = navigationEntry.loadEventEnd;
      this.logMetric('TTI', this.metrics.tti);
    }
  }

  logMetric(name, value) {
    const threshold = this.getThreshold(name);
    const status = value <= threshold ? '✅' : '⚠️';
    console.log(`${status} ${name}: ${Math.round(value)}ms (threshold: ${threshold}ms)`);
  }

  getThreshold(metric) {
    const thresholds = {
      FCP: 1800,  // Good: < 1.8s
      LCP: 2500,  // Good: < 2.5s
      FID: 100,   // Good: < 100ms
      CLS: 0.1,   // Good: < 0.1
      TTI: 3800   // Good: < 3.8s
    };
    return thresholds[metric] || 0;
  }

  getMetrics() {
    return { ...this.metrics };
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

/**
 * Debounce function to optimize performance of frequent operations
 */
export function debounce(func, wait, immediate = false) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
}

/**
 * Throttle function to limit the rate of function execution
 */
export function throttle(func, limit) {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Optimize Firebase real-time listeners with connection management
 */
export class FirebaseListenerManager {
  constructor() {
    this.listeners = new Map();
    this.connectionState = 'connected';
    this.retryCount = 0;
    this.maxRetries = 3;
  }

  addListener(key, unsubscribe) {
    // Clean up existing listener if it exists
    if (this.listeners.has(key)) {
      this.listeners.get(key)();
    }
    this.listeners.set(key, unsubscribe);
  }

  removeListener(key) {
    if (this.listeners.has(key)) {
      this.listeners.get(key)();
      this.listeners.delete(key);
    }
  }

  removeAllListeners() {
    this.listeners.forEach(unsubscribe => unsubscribe());
    this.listeners.clear();
  }

  handleConnectionError(error, key) {
    console.warn(`Firebase listener error for ${key}:`, error);
    
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      setTimeout(() => {
        console.log(`Retrying Firebase connection for ${key} (attempt ${this.retryCount})`);
        // Retry logic would go here
      }, Math.pow(2, this.retryCount) * 1000); // Exponential backoff
    }
  }

  optimizeListener(callback, delay = 100) {
    return debounce(callback, delay);
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Global Firebase listener manager
export const firebaseListenerManager = new FirebaseListenerManager();

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    performanceMonitor.destroy();
    firebaseListenerManager.removeAllListeners();
  });
}
