/* Guest Notification Styles */
.guest-notification {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a365d 0%, #2d3748 100%);
  color: white;
  border-bottom: 3px solid #d69e2e;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  animation: slideDown 0.3s ease-out;
}

.guest-notification-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  max-width: 1200px;
  margin: 0 auto;
  gap: 20px;
}

.guest-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.guest-icon {
  font-size: 1.5rem;
  animation: wave 2s ease-in-out infinite;
}

.guest-text strong {
  display: block;
  font-size: 1rem;
  margin-bottom: 4px;
  color: #d69e2e;
}

.guest-text p {
  margin: 0;
  font-size: 0.9rem;
  color: #e2e8f0;
  line-height: 1.4;
}

.guest-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.guest-signup-btn,
.guest-signin-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.guest-signup-btn {
  background: #d69e2e;
  color: #1a202c;
}

.guest-signup-btn:hover {
  background: #b7791f;
  transform: translateY(-1px);
}

.guest-signin-btn {
  background: transparent;
  color: #e2e8f0;
  border: 1px solid #4a5568;
}

.guest-signin-btn:hover {
  background: #4a5568;
  border-color: #718096;
}

.minimize-btn {
  background: none;
  border: none;
  color: #a0aec0;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.minimize-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
}

/* Minimized state */
.guest-notification.minimized {
  padding: 0;
  background: #2d3748;
  border-bottom: 2px solid #d69e2e;
}

.expand-btn {
  background: none;
  border: none;
  color: #d69e2e;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 16px;
  transition: all 0.2s ease;
  width: 100%;
  text-align: left;
}

.expand-btn:hover {
  background: rgba(214, 158, 46, 0.1);
  color: #b7791f;
}

/* Animations */
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes wave {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(20deg);
  }
  75% {
    transform: rotate(-20deg);
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .guest-notification-content {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }

  .guest-info {
    text-align: center;
  }

  .guest-text p {
    font-size: 0.85rem;
  }

  .guest-actions {
    width: 100%;
    justify-content: center;
  }

  .guest-signup-btn,
  .guest-signin-btn {
    flex: 1;
    justify-content: center;
    max-width: 120px;
  }
}

@media (max-width: 480px) {
  .guest-notification-content {
    padding: 10px 12px;
  }

  .guest-info {
    gap: 8px;
  }

  .guest-icon {
    font-size: 1.2rem;
  }

  .guest-text strong {
    font-size: 0.9rem;
  }

  .guest-text p {
    font-size: 0.8rem;
  }

  .guest-actions {
    gap: 8px;
  }

  .guest-signup-btn,
  .guest-signin-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}
