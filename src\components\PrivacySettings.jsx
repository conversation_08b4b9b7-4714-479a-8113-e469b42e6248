import React, { useState, useEffect } from 'react';
import { useAuth } from '../AuthContext';
import {
  getUserPrivacySettings,
  updatePrivacySettings,
  DEFAULT_PRIVACY_SETTINGS
} from '../services/privacy';
import './PrivacySettings.css';

/**
 * Privacy Settings Component
 * Allows users to control their privacy and connection settings
 */
export default function PrivacySettings({ onClose }) {
  const { currentUser } = useAuth();
  const [settings, setSettings] = useState(DEFAULT_PRIVACY_SETTINGS);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeSection, setActiveSection] = useState('connections');

  useEffect(() => {
    if (!currentUser) return;

    const loadSettings = async () => {
      try {
        const userSettings = await getUserPrivacySettings(currentUser.uid);
        setSettings(userSettings);
      } catch (error) {
        console.error('Error loading privacy settings:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, [currentUser]);

  const handleSettingChange = (section, key, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
  };

  const handleSave = async () => {
    if (!currentUser) return;

    setSaving(true);
    try {
      await updatePrivacySettings(currentUser.uid, settings);
      alert('Privacy settings saved successfully!');
    } catch (error) {
      console.error('Error saving privacy settings:', error);
      alert('Failed to save privacy settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const renderConnectionsSettings = () => (
    <div className="privacy-section">
      <h3>🤝 Friend Requests & Connections</h3>
      
      <div className="setting-group">
        <label className="setting-label">Who can send you friend requests?</label>
        <select
          value={settings.friendRequests.whoCanSendRequests}
          onChange={(e) => handleSettingChange('friendRequests', 'whoCanSendRequests', e.target.value)}
          className="setting-select"
        >
          <option value="everyone">Everyone</option>
          <option value="friends_of_friends">Friends of friends only</option>
          <option value="no_one">No one</option>
        </select>
        <small className="setting-description">
          Control who can send you friend requests on NAROOP
        </small>
      </div>

      <div className="setting-group">
        <label className="setting-checkbox">
          <input
            type="checkbox"
            checked={settings.friendRequests.requireMessage}
            onChange={(e) => handleSettingChange('friendRequests', 'requireMessage', e.target.checked)}
          />
          <span className="checkmark"></span>
          Require a message with friend requests
        </label>
        <small className="setting-description">
          People must include a personal message when sending you a friend request
        </small>
      </div>

      <div className="setting-group">
        <label className="setting-checkbox">
          <input
            type="checkbox"
            checked={settings.friendRequests.autoAcceptFromMutualFriends}
            onChange={(e) => handleSettingChange('friendRequests', 'autoAcceptFromMutualFriends', e.target.checked)}
          />
          <span className="checkmark"></span>
          Auto-accept requests from users with mutual friends
        </label>
        <small className="setting-description">
          Automatically accept friend requests from people who share mutual friends with you
        </small>
      </div>
    </div>
  );

  const renderProfileSettings = () => (
    <div className="privacy-section">
      <h3>👤 Profile Visibility</h3>
      
      <div className="setting-group">
        <label className="setting-label">Who can see your profile?</label>
        <select
          value={settings.profileVisibility.whoCanSeeProfile}
          onChange={(e) => handleSettingChange('profileVisibility', 'whoCanSeeProfile', e.target.value)}
          className="setting-select"
        >
          <option value="everyone">Everyone</option>
          <option value="community">NAROOP community members</option>
          <option value="friends_only">Friends only</option>
          <option value="private">Private (only you)</option>
        </select>
      </div>

      <div className="setting-group">
        <label className="setting-label">Who can see your stories?</label>
        <select
          value={settings.profileVisibility.whoCanSeeStories}
          onChange={(e) => handleSettingChange('profileVisibility', 'whoCanSeeStories', e.target.value)}
          className="setting-select"
        >
          <option value="everyone">Everyone</option>
          <option value="community">NAROOP community members</option>
          <option value="friends_only">Friends only</option>
          <option value="private">Private (only you)</option>
        </select>
      </div>

      <div className="setting-group">
        <label className="setting-label">Who can see your friends list?</label>
        <select
          value={settings.profileVisibility.whoCanSeeFriends}
          onChange={(e) => handleSettingChange('profileVisibility', 'whoCanSeeFriends', e.target.value)}
          className="setting-select"
        >
          <option value="everyone">Everyone</option>
          <option value="community">NAROOP community members</option>
          <option value="friends_only">Friends only</option>
          <option value="private">Private (only you)</option>
        </select>
      </div>

      <div className="setting-group">
        <label className="setting-checkbox">
          <input
            type="checkbox"
            checked={settings.profileVisibility.showOnlineStatus}
            onChange={(e) => handleSettingChange('profileVisibility', 'showOnlineStatus', e.target.checked)}
          />
          <span className="checkmark"></span>
          Show when you're online
        </label>
      </div>

      <div className="setting-group">
        <label className="setting-checkbox">
          <input
            type="checkbox"
            checked={settings.profileVisibility.showLastSeen}
            onChange={(e) => handleSettingChange('profileVisibility', 'showLastSeen', e.target.checked)}
          />
          <span className="checkmark"></span>
          Show when you were last seen
        </label>
      </div>
    </div>
  );

  const renderSearchSettings = () => (
    <div className="privacy-section">
      <h3>🔍 Search & Discovery</h3>
      
      <div className="setting-group">
        <label className="setting-checkbox">
          <input
            type="checkbox"
            checked={settings.searchability.allowInSearch}
            onChange={(e) => handleSettingChange('searchability', 'allowInSearch', e.target.checked)}
          />
          <span className="checkmark"></span>
          Allow others to find you in search
        </label>
        <small className="setting-description">
          Your profile will appear when people search for users
        </small>
      </div>

      <div className="setting-group">
        <label className="setting-checkbox">
          <input
            type="checkbox"
            checked={settings.searchability.allowInSuggestions}
            onChange={(e) => handleSettingChange('searchability', 'allowInSuggestions', e.target.checked)}
          />
          <span className="checkmark"></span>
          Allow friend suggestions
        </label>
        <small className="setting-description">
          You may be suggested as a friend to other users
        </small>
      </div>

      <div className="setting-group">
        <label className="setting-checkbox">
          <input
            type="checkbox"
            checked={settings.searchability.allowStoryBasedSuggestions}
            onChange={(e) => handleSettingChange('searchability', 'allowStoryBasedSuggestions', e.target.checked)}
          />
          <span className="checkmark"></span>
          Allow story-based suggestions
        </label>
        <small className="setting-description">
          Be suggested to users who liked similar stories
        </small>
      </div>
    </div>
  );

  const renderCommunicationSettings = () => (
    <div className="privacy-section">
      <h3>💬 Communication</h3>
      
      <div className="setting-group">
        <label className="setting-label">Who can send you messages?</label>
        <select
          value={settings.communication.whoCanMessage}
          onChange={(e) => handleSettingChange('communication', 'whoCanMessage', e.target.value)}
          className="setting-select"
        >
          <option value="everyone">Everyone</option>
          <option value="friends_only">Friends only</option>
          <option value="no_one">No one</option>
        </select>
      </div>

      <div className="setting-group">
        <label className="setting-label">Who can mention you in posts?</label>
        <select
          value={settings.communication.whoCanMention}
          onChange={(e) => handleSettingChange('communication', 'whoCanMention', e.target.value)}
          className="setting-select"
        >
          <option value="everyone">Everyone</option>
          <option value="friends_only">Friends only</option>
          <option value="no_one">No one</option>
        </select>
      </div>

      <div className="setting-group">
        <label className="setting-checkbox">
          <input
            type="checkbox"
            checked={settings.communication.allowGroupInvites}
            onChange={(e) => handleSettingChange('communication', 'allowGroupInvites', e.target.checked)}
          />
          <span className="checkmark"></span>
          Allow group chat invitations
        </label>
      </div>

      <div className="setting-group">
        <label className="setting-checkbox">
          <input
            type="checkbox"
            checked={settings.communication.allowEventInvites}
            onChange={(e) => handleSettingChange('communication', 'allowEventInvites', e.target.checked)}
          />
          <span className="checkmark"></span>
          Allow community event invitations
        </label>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="privacy-settings-modal">
        <div className="privacy-settings-content">
          <div className="loading-state">Loading privacy settings...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="privacy-settings-modal" onClick={(e) => e.target === e.currentTarget && onClose()}>
      <div className="privacy-settings-content">
        <div className="privacy-header">
          <h2>🔒 Privacy & Security Settings</h2>
          <button className="close-privacy-btn" onClick={onClose}>✕</button>
        </div>

        <div className="privacy-tabs">
          <button 
            className={`privacy-tab ${activeSection === 'connections' ? 'active' : ''}`}
            onClick={() => setActiveSection('connections')}
          >
            🤝 Connections
          </button>
          <button 
            className={`privacy-tab ${activeSection === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveSection('profile')}
          >
            👤 Profile
          </button>
          <button 
            className={`privacy-tab ${activeSection === 'search' ? 'active' : ''}`}
            onClick={() => setActiveSection('search')}
          >
            🔍 Discovery
          </button>
          <button 
            className={`privacy-tab ${activeSection === 'communication' ? 'active' : ''}`}
            onClick={() => setActiveSection('communication')}
          >
            💬 Communication
          </button>
        </div>

        <div className="privacy-body">
          {activeSection === 'connections' && renderConnectionsSettings()}
          {activeSection === 'profile' && renderProfileSettings()}
          {activeSection === 'search' && renderSearchSettings()}
          {activeSection === 'communication' && renderCommunicationSettings()}
        </div>

        <div className="privacy-footer">
          <button 
            className="save-privacy-btn"
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? '⏳ Saving...' : '💾 Save Settings'}
          </button>
          <button className="cancel-privacy-btn" onClick={onClose}>
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}
