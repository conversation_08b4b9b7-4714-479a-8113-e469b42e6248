import { db } from '../firebase';
import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { logAdminAction } from './admin';

/**
 * Content Moderation Service
 * Handles content review, approval, rejection, and automated filtering
 */

// Content status types
export const CONTENT_STATUS = {
  PENDING: 'pending',           // Awaiting moderation
  APPROVED: 'approved',         // Approved and live
  REJECTED: 'rejected',         // Rejected by moderator
  FLAGGED: 'flagged',          // Flagged for review
  REMOVED: 'removed',          // Removed after being live
  EDITED: 'edited'             // Edited by moderator
};

// Report reasons
export const REPORT_REASONS = {
  INAPPROPRIATE_CONTENT: 'inappropriate_content',
  HATE_SPEECH: 'hate_speech',
  HARASSMENT: 'harassment',
  SPAM: 'spam',
  MISINFORMATION: 'misinformation',
  COPYRIGHT: 'copyright',
  VIOLENCE: 'violence',
  OTHER: 'other'
};

// Inappropriate content filters (basic implementation)
const INAPPROPRIATE_WORDS = [
  // Add inappropriate words here - keeping it minimal for example
  'spam', 'scam', 'hate', 'violence'
  // In production, use a comprehensive filtering service
];

/**
 * Submit content for moderation
 * @param {Object} content - Content to moderate
 * @param {string} contentType - Type of content (story, comment, etc.)
 * @returns {Object} Moderation result
 */
export async function submitForModeration(content, contentType = 'story') {
  try {
    // Run automated content filtering
    const filterResult = await runContentFilter(content);
    
    const moderationData = {
      contentId: content.id,
      contentType,
      content,
      status: filterResult.requiresReview ? CONTENT_STATUS.FLAGGED : CONTENT_STATUS.PENDING,
      submittedAt: serverTimestamp(),
      submittedBy: content.author,
      filterResults: filterResult,
      priority: filterResult.priority || 'normal'
    };

    await setDoc(doc(db, 'contentModeration', content.id), moderationData);

    // If content passes basic filters, auto-approve for now
    // In production, you might want manual review for all content
    if (!filterResult.requiresReview && filterResult.score < 0.3) {
      await approveContent(content.id, 'SYSTEM', 'Auto-approved');
      return { 
        success: true, 
        status: CONTENT_STATUS.APPROVED,
        message: 'Content approved automatically'
      };
    }

    return { 
      success: true, 
      status: moderationData.status,
      message: 'Content submitted for moderation review'
    };
  } catch (error) {
    console.error('Error submitting content for moderation:', error);
    throw error;
  }
}

/**
 * Run automated content filtering
 * @param {Object} content - Content to filter
 * @returns {Object} Filter results
 */
async function runContentFilter(content) {
  try {
    const text = `${content.title || ''} ${content.content || ''}`.toLowerCase();
    let score = 0;
    let flags = [];
    let requiresReview = false;

    // Check for inappropriate words
    for (const word of INAPPROPRIATE_WORDS) {
      if (text.includes(word.toLowerCase())) {
        score += 0.3;
        flags.push(`Contains potentially inappropriate word: ${word}`);
      }
    }

    // Check for excessive caps (shouting)
    const capsRatio = (text.match(/[A-Z]/g) || []).length / text.length;
    if (capsRatio > 0.5 && text.length > 20) {
      score += 0.2;
      flags.push('Excessive use of capital letters');
    }

    // Check for repeated characters (spam-like)
    if (/(.)\1{4,}/.test(text)) {
      score += 0.3;
      flags.push('Contains repeated characters (possible spam)');
    }

    // Check for external links (might need review)
    if (/https?:\/\//.test(text)) {
      score += 0.1;
      flags.push('Contains external links');
    }

    // Determine if manual review is required
    requiresReview = score >= 0.5 || flags.length >= 3;

    return {
      score,
      flags,
      requiresReview,
      priority: score >= 0.7 ? 'high' : score >= 0.4 ? 'medium' : 'low'
    };
  } catch (error) {
    console.error('Error running content filter:', error);
    return {
      score: 0,
      flags: [],
      requiresReview: false,
      priority: 'low'
    };
  }
}

/**
 * Approve content
 * @param {string} contentId - Content ID
 * @param {string} moderatorId - Moderator ID
 * @param {string} reason - Approval reason
 * @returns {Object} Result object
 */
export async function approveContent(contentId, moderatorId, reason = '') {
  try {
    const batch = writeBatch(db);

    // Update moderation record
    batch.update(doc(db, 'contentModeration', contentId), {
      status: CONTENT_STATUS.APPROVED,
      reviewedBy: moderatorId,
      reviewedAt: serverTimestamp(),
      moderatorNotes: reason
    });

    // Update the actual content to make it live
    batch.update(doc(db, 'stories', contentId), {
      status: 'published',
      moderationStatus: CONTENT_STATUS.APPROVED,
      publishedAt: serverTimestamp()
    });

    await batch.commit();

    // Log the action
    await logAdminAction({
      action: 'CONTENT_APPROVED',
      performedBy: moderatorId,
      targetContentId: contentId,
      details: { reason },
      timestamp: serverTimestamp()
    });

    return { success: true };
  } catch (error) {
    console.error('Error approving content:', error);
    throw error;
  }
}

/**
 * Reject content
 * @param {string} contentId - Content ID
 * @param {string} moderatorId - Moderator ID
 * @param {string} reason - Rejection reason
 * @returns {Object} Result object
 */
export async function rejectContent(contentId, moderatorId, reason) {
  try {
    const batch = writeBatch(db);

    // Update moderation record
    batch.update(doc(db, 'contentModeration', contentId), {
      status: CONTENT_STATUS.REJECTED,
      reviewedBy: moderatorId,
      reviewedAt: serverTimestamp(),
      moderatorNotes: reason
    });

    // Update the actual content
    batch.update(doc(db, 'stories', contentId), {
      status: 'rejected',
      moderationStatus: CONTENT_STATUS.REJECTED,
      rejectionReason: reason
    });

    await batch.commit();

    // Log the action
    await logAdminAction({
      action: 'CONTENT_REJECTED',
      performedBy: moderatorId,
      targetContentId: contentId,
      details: { reason },
      timestamp: serverTimestamp()
    });

    return { success: true };
  } catch (error) {
    console.error('Error rejecting content:', error);
    throw error;
  }
}

/**
 * Remove content that's already live
 * @param {string} contentId - Content ID
 * @param {string} moderatorId - Moderator ID
 * @param {string} reason - Removal reason
 * @returns {Object} Result object
 */
export async function removeContent(contentId, moderatorId, reason) {
  try {
    const batch = writeBatch(db);

    // Update moderation record
    batch.update(doc(db, 'contentModeration', contentId), {
      status: CONTENT_STATUS.REMOVED,
      removedBy: moderatorId,
      removedAt: serverTimestamp(),
      removalReason: reason
    });

    // Update the actual content
    batch.update(doc(db, 'stories', contentId), {
      status: 'removed',
      moderationStatus: CONTENT_STATUS.REMOVED,
      removalReason: reason,
      removedAt: serverTimestamp()
    });

    await batch.commit();

    // Log the action
    await logAdminAction({
      action: 'CONTENT_REMOVED',
      performedBy: moderatorId,
      targetContentId: contentId,
      details: { reason },
      timestamp: serverTimestamp()
    });

    return { success: true };
  } catch (error) {
    console.error('Error removing content:', error);
    throw error;
  }
}

/**
 * Report content
 * @param {string} contentId - Content ID
 * @param {string} reporterId - Reporter ID
 * @param {string} reason - Report reason
 * @param {string} details - Additional details
 * @returns {Object} Result object
 */
export async function reportContent(contentId, reporterId, reason, details = '') {
  try {
    const reportId = `${contentId}_${reporterId}_${Date.now()}`;
    
    const reportData = {
      id: reportId,
      contentId,
      reporterId,
      reason,
      details,
      status: 'pending',
      reportedAt: serverTimestamp()
    };

    await setDoc(doc(db, 'contentReports', reportId), reportData);

    // Update moderation record to flag for review
    const moderationRef = doc(db, 'contentModeration', contentId);
    const moderationDoc = await getDoc(moderationRef);
    
    if (moderationDoc.exists()) {
      await updateDoc(moderationRef, {
        status: CONTENT_STATUS.FLAGGED,
        reportCount: (moderationDoc.data().reportCount || 0) + 1,
        lastReported: serverTimestamp()
      });
    } else {
      // Create moderation record if it doesn't exist
      await setDoc(moderationRef, {
        contentId,
        status: CONTENT_STATUS.FLAGGED,
        reportCount: 1,
        lastReported: serverTimestamp(),
        priority: 'high'
      });
    }

    return { success: true, reportId };
  } catch (error) {
    console.error('Error reporting content:', error);
    throw error;
  }
}

/**
 * Get pending content for moderation
 * @param {number} limitCount - Number of items to return
 * @returns {Array} Array of pending content
 */
export async function getPendingContent(limitCount = 20) {
  try {
    const pendingQuery = query(
      collection(db, 'contentModeration'),
      where('status', 'in', [CONTENT_STATUS.PENDING, CONTENT_STATUS.FLAGGED]),
      orderBy('submittedAt', 'asc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(pendingQuery);
    const pendingContent = [];

    for (const doc of snapshot.docs) {
      const moderationData = doc.data();
      
      // Get the actual content
      const contentDoc = await getDoc(doc(db, 'stories', moderationData.contentId));
      if (contentDoc.exists()) {
        pendingContent.push({
          ...moderationData,
          contentData: contentDoc.data()
        });
      }
    }

    return pendingContent;
  } catch (error) {
    console.error('Error getting pending content:', error);
    return [];
  }
}

/**
 * Get reported content
 * @param {number} limitCount - Number of reports to return
 * @returns {Array} Array of content reports
 */
export async function getReportedContent(limitCount = 20) {
  try {
    const reportsQuery = query(
      collection(db, 'contentReports'),
      where('status', '==', 'pending'),
      orderBy('reportedAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(reportsQuery);
    const reports = [];

    for (const doc of snapshot.docs) {
      const reportData = doc.data();
      
      // Get the reported content
      const contentDoc = await getDoc(doc(db, 'stories', reportData.contentId));
      if (contentDoc.exists()) {
        reports.push({
          ...reportData,
          contentData: contentDoc.data()
        });
      }
    }

    return reports;
  } catch (error) {
    console.error('Error getting reported content:', error);
    return [];
  }
}
