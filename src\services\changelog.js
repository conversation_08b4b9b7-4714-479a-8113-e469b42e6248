/**
 * Changelog Service
 * Manages version history and changelog data for the Naroop application
 */

import { db } from '../firebase';
import { 
  collection, 
  doc, 
  addDoc, 
  getDocs, 
  query, 
  orderBy, 
  updateDoc, 
  deleteDoc,
  onSnapshot 
} from 'firebase/firestore';

// Changelog entry categories
export const CHANGELOG_CATEGORIES = {
  FEATURE: 'feature',
  IMPROVEMENT: 'improvement',
  BUGFIX: 'bugfix',
  SECURITY: 'security',
  BREAKING: 'breaking'
};

// Category display information
export const CATEGORY_INFO = {
  [CHANGELOG_CATEGORIES.FEATURE]: {
    label: 'New Features',
    icon: '✨',
    color: '#27ae60'
  },
  [CHANGELOG_CATEGORIES.IMPROVEMENT]: {
    label: 'Improvements',
    icon: '🚀',
    color: '#3498db'
  },
  [CHANGELOG_CATEGORIES.BUGFIX]: {
    label: 'Bug Fixes',
    icon: '🐛',
    color: '#e74c3c'
  },
  [CHANGELOG_CATEGORIES.SECURITY]: {
    label: 'Security',
    icon: '🔒',
    color: '#f39c12'
  },
  [CHANGELOG_CATEGORIES.BREAKING]: {
    label: 'Breaking Changes',
    icon: '⚠️',
    color: '#e67e22'
  }
};

/**
 * Add a new changelog entry
 * @param {Object} entry - The changelog entry
 * @param {string} entry.version - Version number (e.g., "1.0.0")
 * @param {string} entry.title - Entry title
 * @param {string} entry.description - Detailed description
 * @param {string} entry.category - Category from CHANGELOG_CATEGORIES
 * @param {Date} entry.releaseDate - Release date
 * @param {Array} entry.changes - Array of change descriptions
 * @returns {Promise<string>} Document ID of the created entry
 */
export const addChangelogEntry = async (entry) => {
  try {
    const changelogRef = collection(db, 'changelog');
    const docRef = await addDoc(changelogRef, {
      ...entry,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error adding changelog entry:', error);
    throw error;
  }
};

/**
 * Get all changelog entries, ordered by version (newest first)
 * @returns {Promise<Array>} Array of changelog entries
 */
export const getChangelogEntries = async () => {
  try {
    const changelogRef = collection(db, 'changelog');
    const q = query(changelogRef, orderBy('releaseDate', 'desc'));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error fetching changelog entries:', error);
    throw error;
  }
};

/**
 * Subscribe to changelog updates in real-time
 * @param {Function} callback - Callback function to handle updates
 * @returns {Function} Unsubscribe function
 */
export const subscribeToChangelog = (callback) => {
  try {
    const changelogRef = collection(db, 'changelog');
    const q = query(changelogRef, orderBy('releaseDate', 'desc'));

    return onSnapshot(q, (querySnapshot) => {
      const entries = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      console.log('Firebase changelog query result:', entries);
      callback(entries);
    }, (error) => {
      console.error('Error in changelog subscription:', error);
      // If there's an error (like collection doesn't exist), return empty array
      callback([]);
    });
  } catch (error) {
    console.error('Error subscribing to changelog:', error);
    // Return a dummy unsubscribe function and call callback with empty array
    callback([]);
    return () => {};
  }
};

/**
 * Update an existing changelog entry
 * @param {string} entryId - Document ID of the entry to update
 * @param {Object} updates - Fields to update
 * @returns {Promise<void>}
 */
export const updateChangelogEntry = async (entryId, updates) => {
  try {
    const entryRef = doc(db, 'changelog', entryId);
    await updateDoc(entryRef, {
      ...updates,
      updatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error updating changelog entry:', error);
    throw error;
  }
};

/**
 * Delete a changelog entry
 * @param {string} entryId - Document ID of the entry to delete
 * @returns {Promise<void>}
 */
export const deleteChangelogEntry = async (entryId) => {
  try {
    const entryRef = doc(db, 'changelog', entryId);
    await deleteDoc(entryRef);
  } catch (error) {
    console.error('Error deleting changelog entry:', error);
    throw error;
  }
};

/**
 * Get changelog entries for a specific version
 * @param {string} version - Version number to filter by
 * @returns {Promise<Array>} Array of changelog entries for the version
 */
export const getChangelogByVersion = async (version) => {
  try {
    const entries = await getChangelogEntries();
    return entries.filter(entry => entry.version === version);
  } catch (error) {
    console.error('Error fetching changelog by version:', error);
    throw error;
  }
};

/**
 * Get the latest changelog entry
 * @returns {Promise<Object|null>} Latest changelog entry or null
 */
export const getLatestChangelog = async () => {
  try {
    const entries = await getChangelogEntries();
    return entries.length > 0 ? entries[0] : null;
  } catch (error) {
    console.error('Error fetching latest changelog:', error);
    throw error;
  }
};
