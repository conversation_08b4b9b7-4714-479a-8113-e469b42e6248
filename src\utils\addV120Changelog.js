/**
 * Version 1.2.0 changelog entry
 * Major platform improvements and bug fixes
 */

import { db } from '../firebase';
import { collection, addDoc, getDocs, query, where } from 'firebase/firestore';

export const addV120Changelog = async () => {
  try {
    console.log('🚀 Adding v1.2.0 changelog entry...');
    
    const changelogRef = collection(db, 'changelog');
    
    // Check if v1.2.0 already exists
    const v120Query = query(changelogRef, where('version', '==', '1.2.0'));
    const v120Snapshot = await getDocs(v120Query);
    
    if (v120Snapshot.empty) {
      // Add v1.2.0 entry
      const v120Entry = {
        version: '1.2.0',
        title: 'Major Platform Improvements - Better Performance & Mobile Experience! ⚡',
        description: 'We\'ve made significant improvements to make NAROOP faster, more reliable, and easier to use on all devices. This update focuses on better performance, improved mobile experience, and fixing issues that were affecting your daily use.',
        category: 'improvement',
        releaseDate: new Date().toISOString(),
        changes: [
          'Much faster page loading and smoother interactions throughout the platform',
          'Improved mobile experience with better touch targets and navigation',
          'Fixed issues where buttons and links were hard to tap on mobile devices',
          'Better text readability with improved font sizes and spacing',
          'Smoother animations and transitions when navigating between pages',
          'Fixed story sharing issues - your stories now save more reliably',
          'Improved error messages that are easier to understand',
          'Better performance when viewing long lists of stories or content',
          'Enhanced accessibility for users with disabilities',
          'Fixed layout issues that caused content to appear cut off on some screens',
          'Improved consistency in colors, spacing, and button styles across the platform',
          'Better memory usage - the app now uses less of your device\'s resources'
        ],
        isBreaking: false,
        timestamp: Date.now()
      };
      
      await addDoc(changelogRef, v120Entry);
      console.log('✅ Added v1.2.0 changelog entry');
      return { success: true, message: 'v1.2.0 changelog entry added successfully' };
    } else {
      console.log('ℹ️ v1.2.0 entry already exists');
      return { success: true, message: 'v1.2.0 entry already exists' };
    }
    
  } catch (error) {
    console.error('❌ Error adding v1.2.0 changelog entry:', error);
    return { success: false, error: error.message };
  }
};

// Make it available globally for console access
window.addV120Changelog = addV120Changelog;
