# Naroop Platform Troubleshooting Guide

## Story Submission Issues

### "Missing or insufficient permissions" Error

**Symptoms:**
- Users receive "❌ Failed to save story: Missing or insufficient permissions" when submitting stories
- Form appears to work but submission fails

**Root Causes & Solutions:**

1. **Firebase Security Rules Not Deployed**
   ```bash
   # Deploy security rules to Firebase
   firebase deploy --only firestore:rules
   ```

2. **Environment Variables Missing**
   - Ensure `.env.local` exists with correct Firebase configuration
   - For production, verify environment variables in Netlify dashboard
   - Check that all VITE_FIREBASE_* variables are set

3. **Authentication State Issues**
   - User may not be properly authenticated
   - Check browser console for authentication errors
   - Try logging out and logging back in

4. **Network/Connection Issues**
   - Check internet connection
   - Verify Firebase project is accessible
   - Check browser network tab for failed requests

**Debugging Steps:**
1. Open browser developer tools (F12)
2. Check Console tab for error messages
3. Check Network tab for failed Firebase requests
4. Verify user authentication state in Application > Local Storage

### Form Validation Issues

**Symptoms:**
- Form submits with empty or invalid data
- Validation messages don't appear

**Solutions:**
1. Ensure title is at least 3 characters
2. Ensure content is at least 10 characters
3. Check that all required fields are filled

## Mobile Navigation Issues

### Menu Items Not Visible on Mobile

**Symptoms:**
- Hamburger menu opens but some items are cut off
- Cannot scroll to see all navigation options

**Solutions:**
1. **CSS Updates Applied** - The mobile navigation now uses:
   - `max-height: calc(100vh - 200px)` for better viewport handling
   - `overflow-y: auto` for scrolling when needed
   - Enhanced touch targets (minimum 44px height)

2. **Clear Browser Cache**
   - Hard refresh (Ctrl+F5 or Cmd+Shift+R)
   - Clear browser cache and cookies

3. **Check Screen Size**
   - Issues primarily affect screens smaller than 768px
   - Test on actual mobile devices, not just browser resize

## Firebase Configuration Issues

### Authentication Errors

**Common Error Messages:**
- "Authentication required"
- "Permission denied"
- "User not found"

**Solutions:**
1. **Check Firebase Project Settings**
   - Verify project ID matches configuration
   - Ensure authentication is enabled
   - Check authorized domains include your deployment URL

2. **Environment Variables**
   ```bash
   # Verify environment variables are loaded
   echo $VITE_FIREBASE_PROJECT_ID
   ```

3. **Security Rules**
   - Deploy latest security rules: `firebase deploy --only firestore:rules`
   - Check Firebase Console > Firestore > Rules

### Database Permission Errors

**Error Messages:**
- "Missing or insufficient permissions"
- "Permission denied"

**Solutions:**
1. **Deploy Security Rules**
   ```bash
   firebase deploy --only firestore:rules
   ```

2. **Check User Authentication**
   - Ensure user is logged in
   - Verify user UID matches document author field

3. **Test with Firebase Emulator**
   ```bash
   firebase emulators:start
   ```

## Development Environment Setup

### Local Development Issues

**Prerequisites:**
1. Node.js 18+ installed
2. Firebase CLI installed: `npm install -g firebase-tools`
3. Git repository cloned

**Setup Steps:**
```bash
# Install dependencies
npm install

# Copy environment template
cp .env.example .env.local

# Start development server
npm run dev
```

### Build Issues

**Common Problems:**
1. **Environment Variables Not Set**
   - Ensure all VITE_* variables are defined
   - Check for typos in variable names

2. **Import Errors**
   - Check all import paths are correct
   - Verify all dependencies are installed

3. **Firebase Configuration**
   - Ensure Firebase project exists
   - Verify API keys are valid

## Production Deployment Issues

### Netlify Deployment

**Common Issues:**
1. **Build Fails**
   - Check build logs in Netlify dashboard
   - Verify environment variables are set
   - Ensure all dependencies are in package.json

2. **Firebase Connection Fails**
   - Verify environment variables in Netlify
   - Check Firebase project settings
   - Ensure domain is authorized in Firebase

3. **Routing Issues**
   - Verify `_redirects` file exists in public folder
   - Check netlify.toml configuration

### Performance Issues

**Symptoms:**
- Slow loading times
- High memory usage
- Unresponsive interface

**Solutions:**
1. **Check Network Tab**
   - Look for large file downloads
   - Verify Firebase requests are completing

2. **Clear Browser Data**
   - Clear cache, cookies, and local storage
   - Disable browser extensions

3. **Check Firebase Quotas**
   - Monitor Firebase usage in console
   - Verify not hitting rate limits

## Getting Help

### Debug Information to Collect

When reporting issues, include:
1. Browser and version
2. Device type (mobile/desktop)
3. Error messages from console
4. Steps to reproduce
5. Screenshots if applicable

### Console Commands for Debugging

```javascript
// Check authentication state
console.log('Auth user:', firebase.auth().currentUser);

// Check environment variables
console.log('Firebase config:', {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID
});

// Check local storage
console.log('Local storage:', localStorage);
```

### Contact Information

For persistent issues:
1. Check GitHub issues
2. Review Firebase documentation
3. Contact development team with debug information

## Quick Fixes

### Story Submission Not Working
1. Log out and log back in
2. Clear browser cache
3. Check internet connection
4. Try different browser

### Mobile Menu Not Working
1. Hard refresh page (Ctrl+F5)
2. Clear browser cache
3. Try different mobile browser
4. Check if JavaScript is enabled

### Authentication Issues
1. Clear browser data
2. Disable ad blockers
3. Check if cookies are enabled
4. Try incognito/private browsing mode
