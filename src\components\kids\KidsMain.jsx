import React, { useState, useEffect } from 'react';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import KidsStories from './KidsStories';
import KidsGames from './KidsGames';
import KidsLearning from './KidsLearning';
import KidsHeroes from './KidsHeroes';
import KidsTest from './KidsTest';
import { COPPACompliance, ParentalControls, AgeVerification } from './KidsSafety';
import './kids.css';

const KidsMain = () => {
  const location = useLocation();
  const [parentalMode, setParentalMode] = useState(false);
  const [coppaAccepted, setCoppaAccepted] = useState(false);
  const [showAgeVerification, setShowAgeVerification] = useState(false);
  const [sessionStartTime, setSessionStartTime] = useState(null);

  // Check for COPPA compliance on component mount
  useEffect(() => {
    const coppaStatus = localStorage.getItem('naroop-kids-coppa-accepted');
    if (coppaStatus === 'true') {
      setCoppaAccepted(true);
      setSessionStartTime(Date.now());
    }
  }, []);

  const isActive = (path) => {
    if (path === '/kids' && location.pathname === '/kids') return true;
    if (path !== '/kids' && location.pathname.startsWith(path)) return true;
    return false;
  };

  const handleCoppaAccept = () => {
    localStorage.setItem('naroop-kids-coppa-accepted', 'true');
    setCoppaAccepted(true);
    setSessionStartTime(Date.now());
  };

  const handleCoppaDecline = () => {
    window.location.href = '/';
  };

  const handleParentalControlsClick = () => {
    setShowAgeVerification(true);
  };

  const handleAgeVerified = () => {
    setShowAgeVerification(false);
    setParentalMode(true);
  };

  const handleAgeVerificationFailed = () => {
    setShowAgeVerification(false);
    alert('Parental controls require adult supervision. Please ask a parent or guardian for help.');
  };

  // Show COPPA compliance screen if not accepted
  if (!coppaAccepted) {
    return <COPPACompliance onAccept={handleCoppaAccept} onDecline={handleCoppaDecline} />;
  }

  return (
    <div className="kids-main">
      {/* Kids Header */}
      <header className="kids-header">
        <div className="kids-header-content">
          <Link to="/" className="kids-back-btn">← Back to Main Site</Link>
          <div className="kids-logo">
            <h1>🌟 NAROOP Kids Zone 🌟</h1>
            <p>A special place for young champions to learn, play, and grow!</p>
          </div>
          <button
            className="parental-controls-btn"
            onClick={handleParentalControlsClick}
            title="Parental Controls"
          >
            👨‍👩‍👧‍👦
          </button>
        </div>
      </header>

      {/* Kids Navigation */}
      <nav className="kids-nav">
        <Link 
          to="/kids" 
          className={`kids-nav-btn ${isActive('/kids') ? 'active' : ''}`}
        >
          🏠 Home
        </Link>
        <Link 
          to="/kids/stories" 
          className={`kids-nav-btn ${isActive('/kids/stories') ? 'active' : ''}`}
        >
          📚 Stories
        </Link>
        <Link 
          to="/kids/games" 
          className={`kids-nav-btn ${isActive('/kids/games') ? 'active' : ''}`}
        >
          🎮 Games
        </Link>
        <Link 
          to="/kids/learn" 
          className={`kids-nav-btn ${isActive('/kids/learn') ? 'active' : ''}`}
        >
          🎓 Learn
        </Link>
        <Link 
          to="/kids/heroes" 
          className={`kids-nav-btn ${isActive('/kids/heroes') ? 'active' : ''}`}
        >
          🦸🏾 Heroes
        </Link>
      </nav>

      {/* Parental Controls Panel */}
      {parentalMode && (
        <div className="parental-controls-panel">
          <div className="parental-controls-content">
            <h3>👨‍👩‍👧‍👦 Parental Controls</h3>
            <p>This section is designed for children ages 6-12 with age-appropriate content.</p>
            <div className="safety-features">
              <div className="safety-item">✅ Content is moderated and child-safe</div>
              <div className="safety-item">✅ No personal information collection</div>
              <div className="safety-item">✅ Educational and positive messaging</div>
              <div className="safety-item">✅ COPPA compliant design</div>
            </div>
            <button onClick={() => setParentalMode(false)}>Close</button>
          </div>
        </div>
      )}

      {/* Main Content Area */}
      <main className="kids-content">
        <Routes>
          <Route path="/" element={<KidsHome />} />
          <Route path="/stories" element={<KidsStories />} />
          <Route path="/games" element={<KidsGames />} />
          <Route path="/learn" element={<KidsLearning />} />
          <Route path="/heroes" element={<KidsHeroes />} />
          <Route path="/test" element={<KidsTest />} />
        </Routes>
      </main>

      {/* Age Verification Modal */}
      {showAgeVerification && (
        <AgeVerification
          onVerified={handleAgeVerified}
          onFailed={handleAgeVerificationFailed}
        />
      )}

      {/* Parental Controls Panel */}
      <ParentalControls
        isOpen={parentalMode}
        onClose={() => setParentalMode(false)}
      />

      {/* Kids Footer */}
      <footer className="kids-footer">
        <p>🌈 Remember: You are amazing, you are loved, and you can achieve anything! 🌈</p>
      </footer>
    </div>
  );
};

// Kids Home Component
const KidsHome = () => {
  return (
    <div className="kids-home">
      <section className="kids-welcome">
        <h2>Welcome to Your Special Place! 🎉</h2>
        <p>
          Hi there, amazing kid! This is a special place just for you where you can learn 
          about incredible people who look like you, play fun games, and discover how 
          awesome you are!
        </p>
      </section>

      <section className="kids-features">
        <div className="kids-feature-card">
          <div className="feature-icon">📚</div>
          <h3>Amazing Stories</h3>
          <p>Read about incredible kids and grown-ups who did amazing things!</p>
          <Link to="/kids/stories" className="feature-btn">Read Stories</Link>
        </div>

        <div className="kids-feature-card">
          <div className="feature-icon">🎮</div>
          <h3>Fun Games</h3>
          <p>Play games that help you learn while having tons of fun!</p>
          <Link to="/kids/games" className="feature-btn">Play Games</Link>
        </div>

        <div className="kids-feature-card">
          <div className="feature-icon">🎓</div>
          <h3>Cool Learning</h3>
          <p>Discover amazing facts about history, culture, and science!</p>
          <Link to="/kids/learn" className="feature-btn">Start Learning</Link>
        </div>

        <div className="kids-feature-card">
          <div className="feature-icon">🦸🏾</div>
          <h3>Real Heroes</h3>
          <p>Meet incredible people who changed the world and made it better!</p>
          <Link to="/kids/heroes" className="feature-btn">Meet Heroes</Link>
        </div>
      </section>

      <section className="kids-daily-message">
        <div className="daily-message-card">
          <h3>🌟 Today's Special Message 🌟</h3>
          <p>
            "You are braver than you believe, stronger than you seem, 
            and smarter than you think. Keep being amazing!"
          </p>
        </div>
      </section>
    </div>
  );
};

export default KidsMain;
