import React, { useState, useEffect } from 'react';

/**
 * Reusable confirmation dialog component for content deletion and other critical actions
 */
export default function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  type = 'warning', // 'warning', 'danger', 'info'
  requiresTyping = false,
  confirmationPhrase = '',
  showUndoOption = false,
  undoTimeLimit = 10000, // 10 seconds
  children
}) {
  const [typedPhrase, setTypedPhrase] = useState('');
  const [isConfirmEnabled, setIsConfirmEnabled] = useState(false);
  const [showUndo, setShowUndo] = useState(false);
  const [undoCountdown, setUndoCountdown] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (requiresTyping) {
      setIsConfirmEnabled(typedPhrase === confirmationPhrase);
    } else {
      setIsConfirmEnabled(true);
    }
  }, [typedPhrase, confirmationPhrase, requiresTyping]);

  useEffect(() => {
    if (showUndo && undoCountdown > 0) {
      const timer = setTimeout(() => {
        setUndoCountdown(undoCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (showUndo && undoCountdown === 0) {
      setShowUndo(false);
      onClose();
    }
  }, [showUndo, undoCountdown, onClose]);

  const handleConfirm = async () => {
    if (!isConfirmEnabled || isProcessing) return;

    setIsProcessing(true);
    try {
      await onConfirm();
      
      if (showUndoOption) {
        setShowUndo(true);
        setUndoCountdown(Math.floor(undoTimeLimit / 1000));
      } else {
        onClose();
      }
    } catch (error) {
      console.error('Error during confirmation action:', error);
      // Handle error - could show error message
    }
    setIsProcessing(false);
  };

  const handleUndo = () => {
    setShowUndo(false);
    // Trigger undo action if provided
    if (onConfirm.undo) {
      onConfirm.undo();
    }
    onClose();
  };

  const handleClose = () => {
    if (isProcessing) return;
    setTypedPhrase('');
    setShowUndo(false);
    setUndoCountdown(0);
    onClose();
  };

  if (!isOpen) return null;

  const getDialogClass = () => {
    const baseClass = 'confirmation-dialog';
    switch (type) {
      case 'danger': return `${baseClass} danger`;
      case 'warning': return `${baseClass} warning`;
      case 'info': return `${baseClass} info`;
      default: return baseClass;
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'danger': return '⚠️';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '❓';
    }
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget && !isProcessing) {
      // Add a small delay to prevent rapid state changes that might cause white screen
      setTimeout(() => {
        try {
          handleClose();
        } catch (error) {
          console.error('Error closing confirmation dialog:', error);
        }
      }, 50);
    }
  };

  if (showUndo) {
    return (
      <div className="confirmation-overlay" onClick={handleOverlayClick}>
        <div className="confirmation-dialog undo">
          <div className="undo-content">
            <div className="undo-icon">↶</div>
            <h3>Action Completed</h3>
            <p>You can undo this action within {undoCountdown} seconds</p>
            <div className="undo-countdown">
              <div 
                className="countdown-bar" 
                style={{ 
                  width: `${(undoCountdown / (undoTimeLimit / 1000)) * 100}%` 
                }}
              ></div>
            </div>
            <div className="undo-actions">
              <button 
                className="undo-btn"
                onClick={handleUndo}
              >
                Undo Action
              </button>
              <button 
                className="dismiss-btn"
                onClick={() => setShowUndo(false)}
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="confirmation-overlay" onClick={handleOverlayClick}>
      <div className={getDialogClass()}>
        <div className="confirmation-header">
          <div className="confirmation-icon">{getIcon()}</div>
          <h3>{title}</h3>
          <button 
            className="close-btn"
            onClick={handleClose}
            disabled={isProcessing}
            aria-label="Close dialog"
          >
            ✕
          </button>
        </div>

        <div className="confirmation-body">
          <p>{message}</p>
          
          {children}

          {requiresTyping && (
            <div className="confirmation-typing">
              <label htmlFor="confirmation-input">
                Type "{confirmationPhrase}" to confirm:
              </label>
              <input
                id="confirmation-input"
                type="text"
                value={typedPhrase}
                onChange={(e) => setTypedPhrase(e.target.value)}
                placeholder={confirmationPhrase}
                className="confirmation-input"
                disabled={isProcessing}
              />
            </div>
          )}
        </div>

        <div className="confirmation-actions">
          <button 
            className="cancel-btn"
            onClick={handleClose}
            disabled={isProcessing}
          >
            {cancelText}
          </button>
          <button 
            className={`confirm-btn ${type}`}
            onClick={handleConfirm}
            disabled={!isConfirmEnabled || isProcessing}
          >
            {isProcessing ? 'Processing...' : confirmText}
          </button>
        </div>
      </div>
    </div>
  );
}

/**
 * Hook for managing confirmation dialogs
 */
export function useConfirmation() {
  const [dialogState, setDialogState] = useState({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: null,
    type: 'warning',
    requiresTyping: false,
    confirmationPhrase: '',
    showUndoOption: false
  });

  const showConfirmation = (options) => {
    setDialogState({
      isOpen: true,
      ...options
    });
  };

  const hideConfirmation = () => {
    setDialogState(prev => ({ ...prev, isOpen: false }));
  };

  const ConfirmationComponent = () => (
    <ConfirmationDialog
      {...dialogState}
      onClose={hideConfirmation}
    />
  );

  return {
    showConfirmation,
    hideConfirmation,
    ConfirmationComponent
  };
}
