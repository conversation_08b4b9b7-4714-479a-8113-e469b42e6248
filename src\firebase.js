// Firebase configuration and initialization for NAROOP
import { initializeApp } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'

// Firebase configuration using environment variables for security
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || 'AIzaSyCN6DWF8ecSzAcWLHFzoy0L8vKFnim-fSM',
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || 'naroop-451d1.firebaseapp.com',
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || 'naroop-451d1',
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || 'naroop-451d1.appspot.com',
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || '280835864133',
  appId: import.meta.env.VITE_FIREBASE_APP_ID || '1:280835864133:web:5fcf2953fca215a721152c',
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || 'G-E82HQ5J8T4',
}

const app = initializeApp(firebaseConfig)
export const auth = getAuth(app)
export const db = getFirestore(app)
