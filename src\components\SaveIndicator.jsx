import React, { useState, useEffect } from 'react';

/**
 * Save Indicator Component
 * Provides visual feedback for save operations
 */
export default function SaveIndicator({ isVisible, type = 'saving', message }) {
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setShow(true);
    } else {
      const timer = setTimeout(() => setShow(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isVisible]);

  if (!show) return null;

  const getIcon = () => {
    switch (type) {
      case 'saving':
        return '💾';
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'syncing':
        return '🔄';
      default:
        return '💾';
    }
  };

  const getClassName = () => {
    const baseClass = 'save-indicator';
    const typeClass = `save-indicator--${type}`;
    const visibleClass = isVisible ? 'save-indicator--visible' : 'save-indicator--hidden';
    return `${baseClass} ${typeClass} ${visibleClass}`;
  };

  return (
    <div className={getClassName()}>
      <div className="save-indicator__content">
        <span className="save-indicator__icon">{getIcon()}</span>
        <span className="save-indicator__message">
          {message || (type === 'saving' ? 'Saving...' : type === 'success' ? 'Saved!' : 'Error saving')}
        </span>
      </div>
      {type === 'saving' && (
        <div className="save-indicator__progress">
          <div className="save-indicator__progress-bar"></div>
        </div>
      )}
    </div>
  );
}
