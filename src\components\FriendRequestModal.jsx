import React, { useState, useEffect } from 'react';
import { useAuth } from '../AuthContext';
import {
  acceptFriendRequest,
  declineFriendRequest,
  getPendingFriendRequests
} from '../services/friends';
import { getDoc, doc } from 'firebase/firestore';
import { db } from '../firebase';
import PresenceIndicator, { PresenceStatus } from './PresenceIndicator';
import './FriendRequestModal.css';

/**
 * Friend Request Modal Component
 * Displays friend request details with accept/decline options
 * Triggered from notification clicks
 */
export default function FriendRequestModal({ 
  isOpen, 
  onClose, 
  requestId = null,
  fromUserId = null,
  onRequestHandled = null 
}) {
  const { currentUser } = useAuth();
  const [request, setRequest] = useState(null);
  const [senderInfo, setSenderInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!isOpen || !currentUser) {
      setRequest(null);
      setSenderInfo(null);
      setLoading(true);
      setError(null);
      return;
    }

    loadRequestData();
  }, [isOpen, currentUser, requestId, fromUserId]);

  const loadRequestData = async () => {
    try {
      setLoading(true);
      setError(null);

      // If we have a specific requestId, try to find it
      if (requestId) {
        const pendingRequests = await getPendingFriendRequests(currentUser.uid);
        const foundRequest = pendingRequests.find(req => req.id === requestId);
        
        if (foundRequest) {
          setRequest(foundRequest);
          setSenderInfo(foundRequest.userInfo);
        } else {
          setError('Friend request not found or already handled');
        }
      } 
      // If we have fromUserId, find the request from that user
      else if (fromUserId) {
        const pendingRequests = await getPendingFriendRequests(currentUser.uid);
        const foundRequest = pendingRequests.find(req => req.fromUserId === fromUserId);
        
        if (foundRequest) {
          setRequest(foundRequest);
          setSenderInfo(foundRequest.userInfo);
        } else {
          // Try to get user info even if request doesn't exist
          try {
            const userDoc = await getDoc(doc(db, 'users', fromUserId));
            if (userDoc.exists()) {
              setSenderInfo(userDoc.data());
              setError('Friend request not found or already handled');
            } else {
              setError('User not found');
            }
          } catch (err) {
            setError('Unable to load user information');
          }
        }
      } else {
        setError('No request information provided');
      }
    } catch (err) {
      console.error('Error loading friend request:', err);
      setError('Failed to load friend request details');
    } finally {
      setLoading(false);
    }
  };

  const handleAccept = async () => {
    if (!request || processing) return;

    setProcessing(true);
    try {
      await acceptFriendRequest(request.id, currentUser.uid);
      
      // Notify parent component
      if (onRequestHandled) {
        onRequestHandled('accepted', request);
      }
      
      // Close modal after short delay to show success
      setTimeout(() => {
        onClose();
      }, 1000);
      
    } catch (err) {
      console.error('Error accepting friend request:', err);
      setError('Failed to accept friend request. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const handleDecline = async () => {
    if (!request || processing) return;

    setProcessing(true);
    try {
      await declineFriendRequest(request.id, currentUser.uid);
      
      // Notify parent component
      if (onRequestHandled) {
        onRequestHandled('declined', request);
      }
      
      // Close modal after short delay to show action completed
      setTimeout(() => {
        onClose();
      }, 500);
      
    } catch (err) {
      console.error('Error declining friend request:', err);
      setError('Failed to decline friend request. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget && !processing) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="friend-request-modal-overlay" onClick={handleOverlayClick}>
      <div className="friend-request-modal">
        <div className="friend-request-modal-header">
          <h3>Friend Request</h3>
          <button 
            className="close-btn"
            onClick={onClose}
            disabled={processing}
            aria-label="Close modal"
          >
            ✕
          </button>
        </div>

        <div className="friend-request-modal-body">
          {loading ? (
            <div className="loading-state">
              <div className="loading-spinner"></div>
              <p>Loading friend request...</p>
            </div>
          ) : error ? (
            <div className="error-state">
              <div className="error-icon">⚠️</div>
              <p>{error}</p>
              {senderInfo && (
                <div className="sender-info-fallback">
                  <PresenceStatus
                    userId={fromUserId}
                    userInfo={senderInfo}
                    showAvatar={true}
                    showName={true}
                    showStatus={false}
                    className="sender-display"
                  />
                </div>
              )}
            </div>
          ) : request && senderInfo ? (
            <div className="request-content">
              <div className="sender-info">
                <PresenceStatus
                  userId={request.fromUserId}
                  userInfo={senderInfo}
                  showAvatar={true}
                  showName={true}
                  showStatus={true}
                  className="sender-display"
                />
              </div>
              
              {request.message && (
                <div className="request-message">
                  <h4>Message:</h4>
                  <p>"{request.message}"</p>
                </div>
              )}
              
              <div className="request-info">
                <p className="request-time">
                  Sent {new Date(request.createdAt?.toDate?.() || request.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          ) : (
            <div className="error-state">
              <div className="error-icon">❓</div>
              <p>Unable to load friend request details</p>
            </div>
          )}
        </div>

        {!loading && !error && request && (
          <div className="friend-request-modal-actions">
            <button 
              className="decline-btn"
              onClick={handleDecline}
              disabled={processing}
            >
              {processing ? '⏳' : '❌'} Decline
            </button>
            <button 
              className="accept-btn"
              onClick={handleAccept}
              disabled={processing}
            >
              {processing ? '⏳' : '✅'} Accept
            </button>
          </div>
        )}
        
        {error && !request && (
          <div className="friend-request-modal-actions">
            <button 
              className="close-only-btn"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
