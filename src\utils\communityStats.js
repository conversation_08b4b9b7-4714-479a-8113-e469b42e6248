/**
 * Community Statistics Utility
 * Manages the transition from launch mode to real statistics display
 */

import { db } from '../firebase';
import { collection, getDocs, query, where } from 'firebase/firestore';

/**
 * Configuration for when to show real stats vs launch mode
 */
const STATS_THRESHOLD = {
  MIN_STORIES: 10,      // Minimum stories before showing stats
  MIN_MEMBERS: 5,       // Minimum members before showing stats
  MIN_SUCCESS_STORIES: 1 // Minimum success stories before showing stats
};

/**
 * Fetch real community statistics from Firebase
 * @returns {Promise<Object|null>} Statistics object or null if insufficient data
 */
export const fetchCommunityStats = async () => {
  try {
    // Fetch stories count
    const storiesRef = collection(db, 'stories');
    const storiesSnapshot = await getDocs(storiesRef);
    const storiesCount = storiesSnapshot.size;

    // Fetch users count
    const usersRef = collection(db, 'users');
    const usersSnapshot = await getDocs(usersRef);
    const membersCount = usersSnapshot.size;

    // Fetch success stories (stories with high engagement or special tags)
    const successStoriesQuery = query(
      storiesRef, 
      where('isSuccessStory', '==', true)
    );
    const successStoriesSnapshot = await getDocs(successStoriesQuery);
    const successStoriesCount = successStoriesSnapshot.size;

    // Check if we have enough data to show real stats
    const hasEnoughData = 
      storiesCount >= STATS_THRESHOLD.MIN_STORIES &&
      membersCount >= STATS_THRESHOLD.MIN_MEMBERS &&
      successStoriesCount >= STATS_THRESHOLD.MIN_SUCCESS_STORIES;

    if (!hasEnoughData) {
      return null; // Return null to indicate we should stay in launch mode
    }

    // Return real statistics
    return {
      stories: storiesCount,
      members: membersCount,
      successStories: successStoriesCount,
      support: "24/7", // This can be static or calculated based on support availability
      highlights: await fetchCommunityHighlights()
    };

  } catch (error) {
    console.error('Error fetching community stats:', error);
    return null; // Fall back to launch mode on error
  }
};

/**
 * Fetch community highlights for display
 * @returns {Promise<Array>} Array of highlight objects
 */
const fetchCommunityHighlights = async () => {
  try {
    // This can be expanded to fetch real highlights from Firebase
    // For now, return some example highlights that could be populated
    const highlights = [];

    // Example: Story of the Month
    const storiesRef = collection(db, 'stories');
    const recentStoriesQuery = query(storiesRef, where('monthlyVotes', '>', 0));
    const recentStoriesSnapshot = await getDocs(recentStoriesQuery);
    
    if (recentStoriesSnapshot.size > 0) {
      highlights.push({
        icon: "🏆",
        title: "Story of the Month",
        description: `${recentStoriesSnapshot.size} amazing stories competing this month`
      });
    }

    // Example: Economic Empowerment
    const economicGoalsQuery = query(
      collection(db, 'economicGoals'),
      where('status', '==', 'completed')
    );
    const economicGoalsSnapshot = await getDocs(economicGoalsQuery);
    
    if (economicGoalsSnapshot.size > 0) {
      highlights.push({
        icon: "💼",
        title: "Economic Success",
        description: `${economicGoalsSnapshot.size} members achieved their financial goals`
      });
    }

    return highlights;

  } catch (error) {
    console.error('Error fetching community highlights:', error);
    return [];
  }
};

/**
 * Determine whether to show launch mode or stats mode
 * @returns {Promise<Object>} Object with mode and stats data
 */
export const getCommunityDisplayMode = async () => {
  const stats = await fetchCommunityStats();
  
  return {
    showStats: stats !== null,
    launchMode: stats === null,
    stats: stats
  };
};

/**
 * Mock function to simulate stats for testing
 * Remove this in production
 */
export const getMockStats = () => {
  return {
    stories: 1250,
    members: 850,
    successStories: 75,
    support: "24/7",
    highlights: [
      {
        icon: "🏆",
        title: "Story of the Month",
        description: "Amazing stories shared by our community members"
      },
      {
        icon: "💼",
        title: "Business Connections",
        description: "Members finding opportunities through networking"
      },
      {
        icon: "🎓",
        title: "Educational Growth",
        description: "Learning and development within our community"
      },
      {
        icon: "🤝",
        title: "Support Network",
        description: "Members helping each other achieve their goals"
      }
    ]
  };
};

/**
 * Configuration object for easy adjustment of thresholds
 */
export const COMMUNITY_CONFIG = {
  STATS_THRESHOLD,
  
  // Update these thresholds as needed
  updateThresholds: (newThresholds) => {
    Object.assign(STATS_THRESHOLD, newThresholds);
  },
  
  // Force stats mode for testing
  forceStatsMode: false,
  
  // Force launch mode for testing
  forceLaunchMode: false
};
