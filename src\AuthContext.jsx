import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { auth, db } from './firebase';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  sendPasswordResetEmail
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { initializePresence, cleanupPresence } from './services/presence';

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isGuestMode, setIsGuestMode] = useState(false);

  const signup = useCallback((email, password, profile = {}) => {
    return createUserWithEmailAndPassword(auth, email, password)
      .then(async (cred) => {
        // Create user profile in Firestore with extra fields
        await setDoc(doc(db, 'users', cred.user.uid), {
          email: cred.user.email,
          name: profile.name || '',
          avatar: profile.avatar || '👤',
          joinDate: new Date().toISOString(),
          totalStories: 0,
          totalReactions: 0,
          badges: [],
          roleModel: profile.roleModel || '',
          tradition: profile.tradition || '',
          dream: profile.dream || '',
          bio: profile.bio || '',
          // New fields for richer engagement
          skills: profile.skills || [],
          interests: profile.interests || [],
          goals: profile.goals || '',
          mentorAvailable: profile.mentorAvailable || false
        });
        return cred;
      });
  }, [])

  const login = useCallback((email, password) => {
    return signInWithEmailAndPassword(auth, email, password);
  }, [])

  const logout = useCallback(async () => {
    try {
      // Clean up presence before signing out
      await cleanupPresence();
      return signOut(auth);
    } catch (error) {
      console.error('Error during logout:', error);
      return signOut(auth); // Still sign out even if presence cleanup fails
    }
  }, [])

  const resetPassword = useCallback((email) => {
    return sendPasswordResetEmail(auth, email);
  }, [])

  const fetchUserProfile = useCallback(async (uid) => {
    const ref = doc(db, 'users', uid)
    const snap = await getDoc(ref)
    return snap.exists() ? snap.data() : null
  }, [])

  // Guest mode functions
  const enableGuestMode = useCallback(() => {
    setIsGuestMode(true);
    setLoading(false);
  }, [])

  const exitGuestMode = useCallback(() => {
    setIsGuestMode(false);
  }, [])

  // New community connection functions
  async function sendConnectionRequest(fromUserId, toUserId, message = '') {
    const connectionRef = doc(db, 'connections', `${fromUserId}_${toUserId}`);
    await setDoc(connectionRef, {
      fromUserId,
      toUserId,
      message,
      status: 'pending',
      createdAt: new Date().toISOString(),
      type: 'connection'
    });
  }

  async function acceptConnectionRequest(connectionId) {
    const connectionRef = doc(db, 'connections', connectionId);
    await updateDoc(connectionRef, {
      status: 'accepted',
      acceptedAt: new Date().toISOString()
    });
  }

  async function findMentors(skills, location = null) {
    // Implementation would query users with mentorAvailable: true
    // and matching skills/location
    const mentorsQuery = query(
      collection(db, 'users'),
      where('mentorAvailable', '==', true),
      where('skills', 'array-contains-any', skills)
    );
    const snapshot = await getDocs(mentorsQuery);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async user => {
      setCurrentUser(user);

      if (user) {
        // Initialize presence when user logs in
        try {
          const userProfile = await fetchUserProfile(user.uid);
          await initializePresence(user.uid, {
            name: userProfile?.name || user.displayName || user.email,
            avatar: userProfile?.avatar || '👤',
            email: user.email
          });
        } catch (error) {
          console.error('Error initializing presence:', error);
        }
      } else {
        // Clean up presence when user logs out
        await cleanupPresence();
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    isGuestMode,
    signup,
    login,
    logout,
    resetPassword,
    fetchUserProfile,
    sendConnectionRequest,
    acceptConnectionRequest,
    findMentors,
    enableGuestMode,
    exitGuestMode
  };

  return (
    <AuthContext.Provider value={value}>
      {(!loading || isGuestMode) && children}
    </AuthContext.Provider>
  );
}

export { AuthContext };
