import React, { useState, useEffect } from 'react';
import { useAuth } from '../AuthContext';
import { getUserRole, ADMIN_ROLES, setUserRole, initializeAdminSystem } from '../services/admin';

/**
 * Admin Debug Helper Component
 * Helps diagnose admin role issues and provides manual assignment if needed
 * TEMPORARY COMPONENT FOR DEBUGGING
 */
export default function AdminDebugHelper({ onClose }) {
  const { currentUser } = useAuth();
  const [userRole, setUserRoleState] = useState(null);
  const [loading, setLoading] = useState(true);
  const [assigning, setAssigning] = useState(false);
  const [debugInfo, setDebugInfo] = useState({});

  useEffect(() => {
    if (!currentUser) return;

    const checkAdminAccess = async () => {
      try {
        console.log('🔍 Debug: Checking admin access for:', currentUser.email);
        
        // Get current role
        const role = await getUserRole(currentUser.uid);
        setUserRoleState(role);
        
        // Set debug info
        setDebugInfo({
          userId: currentUser.uid,
          email: currentUser.email,
          expectedEmail: '<EMAIL>',
          emailMatch: currentUser.email === '<EMAIL>',
          currentRole: role?.role || 'No role found',
          hasPermissions: role?.permissions || 'No permissions',
          roleData: role
        });
        
        console.log('🔍 Debug Info:', {
          userId: currentUser.uid,
          email: currentUser.email,
          expectedEmail: '<EMAIL>',
          emailMatch: currentUser.email === '<EMAIL>',
          currentRole: role?.role || 'No role found',
          roleData: role
        });
        
      } catch (error) {
        console.error('❌ Error checking admin access:', error);
        setDebugInfo({ error: error.message });
      } finally {
        setLoading(false);
      }
    };

    checkAdminAccess();
  }, [currentUser]);

  const handleManualAdminAssignment = async () => {
    if (!currentUser) return;

    setAssigning(true);
    try {
      console.log('🔧 Manually assigning platform owner role...');
      console.log('User email:', currentUser.email);
      console.log('Expected email:', '<EMAIL>');

      // Check if email matches exactly
      if (currentUser.email !== '<EMAIL>') {
        throw new Error(`Email mismatch. Expected: <EMAIL>, Got: ${currentUser.email}`);
      }

      // Force assign platform owner role
      await setUserRole(currentUser.uid, ADMIN_ROLES.PLATFORM_OWNER, 'MANUAL_DEBUG_ASSIGNMENT');

      // Re-initialize admin system
      await initializeAdminSystem(currentUser.uid, currentUser.email);

      // Refresh role
      const newRole = await getUserRole(currentUser.uid);
      setUserRoleState(newRole);

      console.log('✅ Manual admin assignment completed');
      alert('Platform owner role assigned successfully! Please refresh the page.');
    } catch (error) {
      console.error('❌ Error in manual admin assignment:', error);
      alert('Error assigning admin role: ' + error.message);
    } finally {
      setAssigning(false);
    }
  };

  const handleReinitializeAdmin = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      console.log('🔄 Re-initializing admin system...');
      await initializeAdminSystem(currentUser.uid, currentUser.email);
      
      // Refresh role
      const newRole = await getUserRole(currentUser.uid);
      setUserRoleState(newRole);
      
      console.log('✅ Admin system re-initialized');
      alert('Admin system re-initialized! Please refresh the page.');
    } catch (error) {
      console.error('❌ Error re-initializing admin system:', error);
      alert('Error re-initializing: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="admin-test-overlay">
        <div className="admin-test-container">
          <div className="admin-test-header">
            <h2>🔍 Admin Debug Helper</h2>
            <button className="close-test-btn" onClick={onClose}>×</button>
          </div>
          <div className="admin-test-content">
            <p>Loading admin debug information...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-test-overlay">
      <div className="admin-test-container">
        <div className="admin-test-header">
          <h2>🔍 Admin Debug Helper</h2>
          <button className="close-test-btn" onClick={onClose}>×</button>
        </div>
        
        <div className="admin-test-content">
          <div className="debug-section">
            <h3>🔍 Debug Information</h3>
            <div style={{ background: '#f8f9fa', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <p><strong>User ID:</strong> {debugInfo.userId}</p>
              <p><strong>Email:</strong> {debugInfo.email}</p>
              <p><strong>Expected Email:</strong> {debugInfo.expectedEmail}</p>
              <p><strong>Email Match:</strong> {debugInfo.emailMatch ? '✅ YES' : '❌ NO'}</p>
              <p><strong>Current Role:</strong> {debugInfo.currentRole}</p>
              <p><strong>Role Data:</strong> {JSON.stringify(debugInfo.roleData, null, 2)}</p>
            </div>
          </div>

          <div className="debug-section">
            <h3>🛠️ Actions</h3>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
              <button
                onClick={handleReinitializeAdmin}
                disabled={loading}
                style={{
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  padding: '10px 15px',
                  borderRadius: '5px',
                  cursor: 'pointer'
                }}
              >
                🔄 Re-initialize Admin System
              </button>
              
              <button
                onClick={handleManualAdminAssignment}
                disabled={assigning}
                style={{
                  backgroundColor: '#d4af37',
                  color: 'white',
                  border: 'none',
                  padding: '10px 15px',
                  borderRadius: '5px',
                  cursor: assigning ? 'not-allowed' : 'pointer',
                  opacity: assigning ? 0.6 : 1
                }}
              >
                {assigning ? 'Assigning...' : '🛡️ Force Assign Platform Owner Role'}
              </button>
            </div>
          </div>

          <div className="debug-section">
            <h3>📋 Instructions</h3>
            <ol>
              <li>Check the debug information above</li>
              <li>If "Email Match" shows ❌ NO, verify you're logged in with the correct email</li>
              <li>If "Current Role" shows "user" or "No role found", try "Re-initialize Admin System"</li>
              <li>If that doesn't work, try "Force Assign Platform Owner Role"</li>
              <li>After any action, refresh the page to see if the 🛡️ Admin button appears</li>
            </ol>
          </div>
        </div>

        <div className="admin-test-footer">
          <button className="close-test-btn-large" onClick={onClose}>
            Close Debug Helper
          </button>
        </div>
      </div>
    </div>
  );
}
