import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '../AuthContext';
import { collection, query, orderBy, limit, onSnapshot, where } from 'firebase/firestore';
import { db } from '../firebase';

/**
 * Dynamic Newsfeed Component
 * Displays personalized feed of recent activities, stories, and updates
 */
export default function DynamicNewsfeed({ onClose }) {
  const { currentUser } = useAuth();
  const [feedItems, setFeedItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // 'all', 'stories', 'support', 'discussions', 'campaigns'
  const [timeFilter, setTimeFilter] = useState('week'); // 'day', 'week', 'month', 'all'
  const [followedUsers, setFollowedUsers] = useState([]);
  const [followedTopics, setFollowedTopics] = useState([]);

  useEffect(() => {
    if (!currentUser) return;

    // Load user's followed users and topics
    loadUserPreferences();

    // Set up real-time listeners for different content types
    const unsubscribers = [];

    // Stories
    const storiesQuery = query(
      collection(db, 'stories'),
      orderBy('createdAt', 'desc'),
      limit(20)
    );
    unsubscribers.push(
      onSnapshot(storiesQuery, (snapshot) => {
        const stories = snapshot.docs.map(doc => ({
          id: doc.id,
          type: 'story',
          ...doc.data(),
          timestamp: doc.data().createdAt?.toDate() || new Date()
        }));
        updateFeedItems('stories', stories);
      })
    );

    // Support Requests
    const supportQuery = query(
      collection(db, 'supportRequests'),
      orderBy('createdAt', 'desc'),
      limit(15)
    );
    unsubscribers.push(
      onSnapshot(supportQuery, (snapshot) => {
        const requests = snapshot.docs.map(doc => ({
          id: doc.id,
          type: 'support',
          ...doc.data(),
          timestamp: doc.data().createdAt?.toDate() || new Date()
        }));
        updateFeedItems('support', requests);
      })
    );

    // Discussions
    const discussionsQuery = query(
      collection(db, 'discussions'),
      orderBy('lastActivity', 'desc'),
      limit(15)
    );
    unsubscribers.push(
      onSnapshot(discussionsQuery, (snapshot) => {
        const discussions = snapshot.docs.map(doc => ({
          id: doc.id,
          type: 'discussion',
          ...doc.data(),
          timestamp: doc.data().lastActivity?.toDate() || new Date()
        }));
        updateFeedItems('discussions', discussions);
      })
    );

    // Activism Campaigns
    const campaignsQuery = query(
      collection(db, 'activismCampaigns'),
      orderBy('createdAt', 'desc'),
      limit(10)
    );
    unsubscribers.push(
      onSnapshot(campaignsQuery, (snapshot) => {
        const campaigns = snapshot.docs.map(doc => ({
          id: doc.id,
          type: 'campaign',
          ...doc.data(),
          timestamp: doc.data().createdAt?.toDate() || new Date()
        }));
        updateFeedItems('campaigns', campaigns);
      })
    );

    setLoading(false);

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, [currentUser]);

  const loadUserPreferences = async () => {
    // In a real implementation, load from user's profile
    // For now, use mock data
    setFollowedUsers(['user1', 'user2', 'user3']);
    setFollowedTopics(['economic-empowerment', 'community-support', 'activism']);
  };

  const updateFeedItems = (type, items) => {
    setFeedItems(prev => {
      const filtered = prev.filter(item => item.type !== type);
      return [...filtered, ...items].sort((a, b) => b.timestamp - a.timestamp);
    });
  };

  const filteredFeedItems = useMemo(() => {
    let filtered = feedItems;

    // Apply content type filter
    if (filter !== 'all') {
      filtered = filtered.filter(item => {
        switch (filter) {
          case 'stories': return item.type === 'story';
          case 'support': return item.type === 'support';
          case 'discussions': return item.type === 'discussion';
          case 'campaigns': return item.type === 'campaign';
          default: return true;
        }
      });
    }

    // Apply time filter
    const now = new Date();
    const timeFilters = {
      day: 24 * 60 * 60 * 1000,
      week: 7 * 24 * 60 * 60 * 1000,
      month: 30 * 24 * 60 * 60 * 1000,
      all: Infinity
    };

    if (timeFilter !== 'all') {
      const cutoff = new Date(now.getTime() - timeFilters[timeFilter]);
      filtered = filtered.filter(item => item.timestamp >= cutoff);
    }

    // Prioritize content from followed users and topics
    return filtered.sort((a, b) => {
      const aIsFollowed = followedUsers.includes(a.authorId) || 
                         (a.tags && a.tags.some(tag => followedTopics.includes(tag)));
      const bIsFollowed = followedUsers.includes(b.authorId) || 
                         (b.tags && b.tags.some(tag => followedTopics.includes(tag)));

      if (aIsFollowed && !bIsFollowed) return -1;
      if (!aIsFollowed && bIsFollowed) return 1;
      return b.timestamp - a.timestamp;
    });
  }, [feedItems, filter, timeFilter, followedUsers, followedTopics]);

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now - timestamp) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return timestamp.toLocaleDateString();
  };

  const getItemIcon = (type) => {
    switch (type) {
      case 'story': return '📖';
      case 'support': return '🤝';
      case 'discussion': return '🗣️';
      case 'campaign': return '✊🏾';
      default: return '📄';
    }
  };

  const getItemColor = (type) => {
    switch (type) {
      case 'story': return '#3b82f6';
      case 'support': return '#10b981';
      case 'discussion': return '#f59e0b';
      case 'campaign': return '#dc2626';
      default: return '#6b7280';
    }
  };

  const renderFeedItem = (item) => {
    const isFollowed = followedUsers.includes(item.authorId) || 
                      (item.tags && item.tags.some(tag => followedTopics.includes(tag)));

    return (
      <div key={`${item.type}-${item.id}`} className={`feed-item ${isFollowed ? 'followed' : ''}`}>
        <div className="feed-item-header">
          <div className="item-type-indicator" style={{ backgroundColor: getItemColor(item.type) }}>
            {getItemIcon(item.type)}
          </div>
          <div className="item-meta">
            <div className="item-author">
              {item.authorName || `User ${item.authorId?.substring(0, 8)}`}
              {isFollowed && <span className="followed-badge">Following</span>}
            </div>
            <div className="item-time">{formatTimeAgo(item.timestamp)}</div>
          </div>
        </div>

        <div className="feed-item-content">
          <h4 className="item-title">{item.title}</h4>
          <p className="item-description">
            {item.description || item.content || item.excerpt || 'No description available'}
          </p>
          
          {item.tags && item.tags.length > 0 && (
            <div className="item-tags">
              {item.tags.slice(0, 3).map(tag => (
                <span key={tag} className="tag">{tag}</span>
              ))}
            </div>
          )}
        </div>

        <div className="feed-item-actions">
          <div className="item-stats">
            {item.type === 'story' && (
              <>
                <span>👍 {item.likes || 0}</span>
                <span>💬 {item.comments?.length || 0}</span>
              </>
            )}
            {item.type === 'support' && (
              <>
                <span>💬 {item.responses?.length || 0} responses</span>
                <span>📍 {item.location}</span>
              </>
            )}
            {item.type === 'discussion' && (
              <>
                <span>💬 {item.responses?.length || 0} replies</span>
                <span>👀 {item.views || 0} views</span>
              </>
            )}
            {item.type === 'campaign' && (
              <>
                <span>✊ {item.supporters?.length || 0} supporters</span>
                <span>🎯 {item.goal}</span>
              </>
            )}
          </div>
          
          <button 
            className="view-item-btn"
            onClick={() => handleViewItem(item)}
          >
            View
          </button>
        </div>
      </div>
    );
  };

  const handleViewItem = (item) => {
    // Navigate to the specific item based on type
    console.log('View item:', item);
    // In a real implementation, this would navigate to the item's detail page
  };

  if (loading) {
    return (
      <div className="newsfeed-system">
        <div className="newsfeed-loading">
          <div className="loading-spinner"></div>
          <p>Loading your personalized feed...</p>
        </div>
      </div>
    );
  }

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setTimeout(() => {
        try {
          onClose();
        } catch (error) {
          console.error('Error closing newsfeed:', error);
        }
      }, 50);
    }
  };

  return (
    <div className="newsfeed-system" onClick={handleOverlayClick}>
      <div className="newsfeed-content">
        <div className="newsfeed-header">
          <h2>📰 Your Feed</h2>
          <button className="close-newsfeed-btn" onClick={onClose}>
            ✕
          </button>
        </div>
        <div className="feed-filters">
          <div className="filter-group">
            <label>Content Type:</label>
            <select value={filter} onChange={(e) => setFilter(e.target.value)}>
              <option value="all">All Content</option>
              <option value="stories">Stories</option>
              <option value="support">Support Requests</option>
              <option value="discussions">Discussions</option>
              <option value="campaigns">Campaigns</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Time Range:</label>
            <select value={timeFilter} onChange={(e) => setTimeFilter(e.target.value)}>
              <option value="day">Last 24 Hours</option>
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>

        <div className="feed-stats">
          <div className="stats-summary">
            <span>{filteredFeedItems.length} items in your feed</span>
            <span>•</span>
            <span>{followedUsers.length} followed users</span>
            <span>•</span>
            <span>{followedTopics.length} followed topics</span>
          </div>
        </div>

        <div className="feed-list">
          {filteredFeedItems.length > 0 ? (
            filteredFeedItems.map(renderFeedItem)
          ) : (
            <div className="empty-feed">
              <div className="empty-icon">📰</div>
              <h3>No content found</h3>
              <p>Try adjusting your filters or follow more users and topics to see more content.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
