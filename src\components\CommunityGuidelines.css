/* Community Guidelines Styles */
.guidelines-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1500;
  padding: var(--space-md);
}

.guidelines-content {
  background: var(--color-light);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
  display: flex;
  flex-direction: column;
}

/* Header */
.guidelines-header {
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  color: var(--color-light);
  padding: var(--space-lg);
  text-align: center;
  position: relative;
}

.guidelines-header h2 {
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-2xl);
  font-weight: 700;
}

.guidelines-header p {
  margin: 0;
  font-size: var(--text-base);
  opacity: 0.9;
}

.close-guidelines-btn {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: var(--color-light);
  border-radius: var(--radius-full);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  cursor: pointer;
  transition: var(--transition-normal);
}

.close-guidelines-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Body */
.guidelines-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-lg);
}

.guideline-section {
  margin-bottom: var(--space-xl);
  padding-bottom: var(--space-lg);
  border-bottom: 1px solid rgba(184, 134, 11, 0.2);
}

.guideline-section:last-child {
  border-bottom: none;
}

.guideline-section h3 {
  color: var(--color-primary);
  margin: 0 0 var(--space-md) 0;
  font-size: var(--text-xl);
  font-weight: 600;
}

.guideline-section p {
  color: var(--color-dark);
  line-height: 1.6;
  margin-bottom: var(--space-md);
}

/* Lists */
.guidelines-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.guidelines-list li {
  padding: var(--space-sm) var(--space-md);
  margin-bottom: var(--space-sm);
  border-radius: var(--radius-md);
  border-left: 4px solid;
  background: rgba(184, 134, 11, 0.05);
}

.guidelines-list.positive li {
  border-left-color: var(--color-accent-green);
  background: rgba(34, 197, 94, 0.1);
}

.guidelines-list.negative li {
  border-left-color: var(--color-accent-red);
  background: rgba(239, 68, 68, 0.1);
}

.guidelines-list li strong {
  color: var(--color-primary);
}

/* Content Standards */
.content-standards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-md);
}

.standard-item {
  background: rgba(184, 134, 11, 0.05);
  border: 2px solid rgba(184, 134, 11, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.standard-item h4 {
  color: var(--color-primary);
  margin: 0 0 var(--space-md) 0;
  font-size: var(--text-lg);
}

.standard-item ul {
  margin: 0;
  padding-left: var(--space-lg);
}

.standard-item li {
  margin-bottom: var(--space-xs);
  color: var(--color-gray-medium);
}

/* Moderation Process */
.moderation-process {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  margin-top: var(--space-md);
}

.process-step {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  background: rgba(184, 134, 11, 0.05);
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--color-primary);
}

.step-number {
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  color: var(--color-light);
  border-radius: var(--radius-full);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.step-content h4 {
  color: var(--color-primary);
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--text-lg);
}

.step-content p {
  margin: 0;
  color: var(--color-gray-medium);
}

/* Contact Info */
.contact-info {
  background: rgba(184, 134, 11, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  border-left: 4px solid var(--color-accent-emerald);
}

.contact-info p {
  margin: 0;
  line-height: 1.8;
}

.contact-info strong {
  color: var(--color-primary);
}

/* Footer */
.guidelines-footer {
  background: linear-gradient(135deg, rgba(184, 134, 11, 0.1), rgba(34, 197, 94, 0.1));
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  border: 2px solid rgba(184, 134, 11, 0.3);
  margin-top: var(--space-lg);
  text-align: center;
  font-style: italic;
  color: var(--color-primary);
}

/* Actions */
.guidelines-actions {
  padding: var(--space-lg);
  border-top: 1px solid rgba(184, 134, 11, 0.2);
  background: var(--color-gray-light);
  text-align: center;
}

.accept-guidelines-btn {
  background: linear-gradient(135deg, var(--color-accent-green), var(--color-accent-emerald));
  color: var(--color-light);
  border: none;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: var(--text-lg);
  cursor: pointer;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.accept-guidelines-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .guidelines-modal {
    padding: var(--space-sm);
  }
  
  .guidelines-content {
    max-height: 95vh;
  }
  
  .guidelines-header {
    padding: var(--space-md);
  }
  
  .guidelines-header h2 {
    font-size: var(--text-xl);
  }
  
  .guidelines-body {
    padding: var(--space-md);
  }
  
  .content-standards {
    grid-template-columns: 1fr;
  }
  
  .process-step {
    flex-direction: column;
    text-align: center;
  }
  
  .step-number {
    align-self: center;
  }
  
  .guidelines-actions {
    padding: var(--space-md);
  }
  
  .accept-guidelines-btn {
    width: 100%;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .guidelines-list li {
    border-left-width: 6px;
  }
  
  .process-step {
    border-left-width: 6px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .close-guidelines-btn,
  .accept-guidelines-btn {
    transition: none;
  }
  
  .close-guidelines-btn:hover,
  .accept-guidelines-btn:hover {
    transform: none;
  }
}
