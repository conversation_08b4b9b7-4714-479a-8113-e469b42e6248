#!/bin/bash

# Naroop Firebase Deployment Script
# This script deploys Firebase security rules and indexes

echo "🚀 Starting Naroop Firebase Deployment..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in to Firebase
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase. Please login first:"
    echo "firebase login"
    exit 1
fi

# Check if firebase.json exists
if [ ! -f "firebase.json" ]; then
    echo "❌ firebase.json not found. Please ensure you're in the project root directory."
    exit 1
fi

# Check if firestore.rules exists
if [ ! -f "firestore.rules" ]; then
    echo "❌ firestore.rules not found. Please ensure the security rules file exists."
    exit 1
fi

echo "📋 Deploying Firestore security rules..."
firebase deploy --only firestore:rules

if [ $? -eq 0 ]; then
    echo "✅ Firestore security rules deployed successfully!"
else
    echo "❌ Failed to deploy Firestore security rules."
    exit 1
fi

echo "📊 Deploying Firestore indexes..."
firebase deploy --only firestore:indexes

if [ $? -eq 0 ]; then
    echo "✅ Firestore indexes deployed successfully!"
else
    echo "❌ Failed to deploy Firestore indexes."
    exit 1
fi

echo "🎉 Firebase deployment completed successfully!"
echo ""
echo "📝 Next steps:"
echo "1. Test story submission functionality"
echo "2. Verify mobile navigation works correctly"
echo "3. Check that all users can access the platform"
echo ""
echo "🔍 To monitor your Firebase project:"
echo "firebase console"
