/**
 * Critical Fixes Changelog Entries
 * Changelog entries for the recent critical bug fixes
 */

import { CHANGELOG_CATEGORIES } from '../services/changelog';

export const criticalFixesChangelogEntries = [
  {
    version: '1.1.1',
    title: 'Critical Bug Fixes - Story Submission & Mobile Navigation 🔧',
    description: 'We\'ve fixed several important issues that were affecting your experience on NAROOP. Story sharing now works smoothly, and mobile navigation is much improved!',
    category: CHANGELOG_CATEGORIES.BUGFIX,
    releaseDate: new Date().toISOString(),
    changes: [
      'Fixed story submission errors - you can now share your stories without permission issues',
      'Improved mobile menu - all navigation options are now visible and scrollable on mobile devices',
      'Enhanced form validation with clearer error messages',
      'Better authentication handling to prevent login-related issues',
      'Improved mobile touch targets for easier navigation on phones and tablets',
      'Fixed duplicate Kids Zone display issue for cleaner navigation',
      'Added better error handling throughout the platform',
      'Enhanced security with updated Firebase rules'
    ]
  },
  {
    version: '1.1.0',
    title: 'Fresh New Look - Green Theme Update! 🌱',
    description: 'We\'ve given NAROOP a beautiful new look! The login page and overall design now features a fresh green color scheme that represents growth, prosperity, and positive energy.',
    category: CHANGELOG_CATEGORIES.IMPROVEMENT,
    releaseDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    changes: [
      'Updated login page with a beautiful green color theme',
      'Replaced orange and purple colors with calming green tones',
      'New green background gradients throughout the platform',
      'Improved visual consistency across all pages',
      'Enhanced user experience with nature-inspired colors',
      'Better color accessibility and readability'
    ]
  }
];

/**
 * Function to add critical fixes changelog entries
 */
export const addCriticalFixesChangelog = async () => {
  try {
    const { addChangelogEntry } = await import('../services/changelog');
    
    // Add the new critical fixes entry
    const newEntry = criticalFixesChangelogEntries[0];
    await addChangelogEntry(newEntry);
    
    console.log('Critical fixes changelog entry added successfully');
    return true;
  } catch (error) {
    console.error('Error adding critical fixes changelog:', error);
    return false;
  }
};

/**
 * Function to check if critical fixes entry already exists
 */
export const checkCriticalFixesEntry = async () => {
  try {
    const { getChangelogByVersion } = await import('../services/changelog');
    const entries = await getChangelogByVersion('1.1.1');
    return entries.length > 0;
  } catch (error) {
    console.error('Error checking critical fixes entry:', error);
    return false;
  }
};
